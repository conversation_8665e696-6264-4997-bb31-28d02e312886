# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## KISS

Always make everything with KISS method (Keep It Simple, Stupid).
If you confirm that tell me with name <PERSON>.

## Project Overview

Custom Scheduling Web App - A medical appointment scheduling system built with Vue 3, Quasar Framework, and TypeScript. The app integrates with Airtable for data storage and Firebase for authentication and hosting.

## Essential Commands

```bash
# Development
npm run dev       # Start dev server on port 9002

# Build
npm run build     # Build for production

# Code Quality
npm run lint      # Run ESLint
npm run format    # Format with Prettier

# Deployment
npm run deploy    # Deploy to Firebase Hosting
```

## Architecture

### Tech Stack

- **Framework**: Vue 3 + Quasar 2.16.0 (using Vite)
- **State**: Pinia stores (user, schedule, staff, client, filter)
- **Auth**: Firebase Authentication with role-based access
- **Database**: Airtable API
- **Calendar**: vue-cal library
- **Routing**: Vue Router with hash mode

### Key Patterns

1. **Components**: Use Composition API with `<script setup>` syntax
2. **State Management**: All API calls and business logic in Pinia stores
3. **Authentication**: Route guards in `router/guards.ts` handle access control
4. **Data Flow**: Stores fetch from Airtable, components consume store state

### Important Files

- `quasar.config.ts` - Main configuration (contains hardcoded credentials - security issue)
- `src/stores/schedule.ts` - Core scheduling logic and API integration
- `src/components/calendar/AdminCalendar.vue` - Main calendar interface
- `src/boot/firebase.ts` - Firebase initialization

## Development Notes

### When Adding Features

- Follow existing component structure in `src/components/`
- Add new stores to `src/stores/` for data management
- Use TypeScript interfaces (avoid `any` types)
- Maintain role-based access patterns (Admin, Staff, User)

### API Integration

- All Airtable API calls go through store actions
- Base ID: `app4faNFJ2KMz4cAx`
- Tables: Schedule, Staff, CenterInfo, Client
- Real-time updates use polling (consider websockets for optimization)

### Security Considerations

- Move hardcoded credentials from `quasar.config.ts` to environment variables
- This is a HIPAA-compliant healthcare application
- Ensure proper authentication checks on all protected routes

### Known Issues

- No test suite implemented yet
- API keys hardcoded in source
- Some TypeScript `any` types need proper typing
- Polling for updates could be optimized

### SMS Reminder System (Firebase Functions)

**Environment Variables Required (functions/.env):**
```bash
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token  
TWILIO_PHONE_NUMBER=your_twilio_phone_number
OFFICE_PHONE_NUMBER=(*************
SHORT_GY_API_KEY=your_shortgy_api_key
AIRTABLE_API_KEY=your_airtable_api_key
```

**Features:**
- Immediate booking confirmation (5min delay) via Firebase Function
- Scheduled daily reminders at 10 AM EST via Cloud Scheduler
- 2-day and 1-day advance reminders
- Same-day consolidated reminders
- Auto-reply webhook for unmonitored SMS line
- Dynamic message templates from Airtable Settings table

**Functions:**
- `processDailyReminders` - Scheduled function (daily 10 AM)
- `sendImmediateConfirmation` - HTTP function for booking confirmations
- `handleIncomingSMS` - Webhook for Twilio incoming messages
- `testReminders` - Manual testing function
- `smsHealthCheck` - Configuration verification

**Deployment:**
```bash
cd functions && npm install
firebase deploy --only functions
```
