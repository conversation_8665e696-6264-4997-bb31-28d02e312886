[debug] [2025-09-01T12:30:48.235Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.236Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.236Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.237Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.237Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.250Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.250Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.250Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.250Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.250Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.251Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.251Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.251Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.252Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.252Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.252Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.252Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.252Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.253Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.253Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.262Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.262Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.262Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.262Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.262Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.263Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.263Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.263Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.263Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.263Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.264Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.264Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.264Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.264Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.264Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.264Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.264Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.264Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.265Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.265Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.265Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.265Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.265Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.265Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.265Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.266Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.266Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.266Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.266Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.266Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.267Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.267Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.267Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.267Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.267Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.267Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.267Z] Checked if tokens are valid: false, expires at: 1756726079113
[debug] [2025-09-01T12:30:48.267Z] > refreshing access token with scopes: []
[debug] [2025-09-01T12:30:48.267Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-09-01T12:30:48.267Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.359Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.359Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.384Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [none]
[debug] [2025-09-01T12:30:48.384Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.389Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.389Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.407Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [none]
[debug] [2025-09-01T12:30:48.407Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.408Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.408Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.413Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ [none]
[debug] [2025-09-01T12:30:48.413Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.413Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.413Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.421Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ [none]
[debug] [2025-09-01T12:30:48.421Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.422Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.422Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.428Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [none]
[debug] [2025-09-01T12:30:48.428Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.428Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.428Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.435Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [none]
[debug] [2025-09-01T12:30:48.435Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.436Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.436Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.443Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ [none]
[debug] [2025-09-01T12:30:48.443Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.444Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.444Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.452Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-09-01T12:30:48.452Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.453Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.454Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.462Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [none]
[debug] [2025-09-01T12:30:48.462Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.463Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.463Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.469Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-09-01T12:30:48.469Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.471Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.471Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.478Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [none]
[debug] [2025-09-01T12:30:48.478Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:48.479Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-09-01T12:30:48.479Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-09-01T12:30:48.484Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-09-01T12:30:48.484Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/mmawellness-2230c
[debug] [2025-09-01T12:30:49.277Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com 403
[debug] [2025-09-01T12:30:49.277Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [omitted]
[debug] [2025-09-01T12:30:49.399Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com 200
[debug] [2025-09-01T12:30:49.399Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [omitted]
[debug] [2025-09-01T12:30:49.457Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-09-01T12:30:49.458Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-09-01T12:30:49.633Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-09-01T12:30:49.633Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-09-01T12:30:49.706Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ 200
[debug] [2025-09-01T12:30:49.706Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/ [omitted]
[debug] [2025-09-01T12:30:50.007Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com 403
[debug] [2025-09-01T12:30:50.008Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [omitted]
[debug] [2025-09-01T12:30:50.010Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com 403
[debug] [2025-09-01T12:30:50.010Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebaseio.com [omitted]
[debug] [2025-09-01T12:30:50.297Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com 200
[debug] [2025-09-01T12:30:50.298Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [omitted]
[debug] [2025-09-01T12:30:50.301Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com 200
[debug] [2025-09-01T12:30:50.301Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasedataconnect.googleapis.com [omitted]
[debug] [2025-09-01T12:30:50.383Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-09-01T12:30:50.383Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/mmawellness-2230c/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-09-01T12:30:50.389Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-09-01T12:30:50.389Z] > authorizing via signed-in user (<EMAIL>)
