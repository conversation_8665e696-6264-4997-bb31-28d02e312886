import { defineStore } from 'pinia';
import { ref } from 'vue';
import axios from 'axios';

interface SettingRecord {
  id: string;
  fields: {
    'Setting name'?: string;
    'Option 1'?: string;
  };
}

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<SettingRecord[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const fetchSettings = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response = await axios.get('/appbAXrcL96xQIa1C/tblPd2cMBzVFBNvSl');
      settings.value = response.data.records || [];
    } catch (err) {
      console.error('Error fetching settings:', err);
      error.value = 'Failed to fetch settings';
    } finally {
      isLoading.value = false;
    }
  };

  const getSetting = (settingName: string): string | null => {
    const setting = settings.value.find(
      s => s.fields['Setting name'] === settingName
    );
    return setting?.fields['Option 1'] || null;
  };


  const updateSetting = async (settingName: string, value: string) => {
    try {
      const setting = settings.value.find(s => s.fields['Setting name'] === settingName);
      
      if (setting) {
        // Update existing setting
        const response = await axios.patch(
          `/appbAXrcL96xQIa1C/tblPd2cMBzVFBNvSl/${setting.id}`,
          {
            fields: {
              'Option 1': value
            }
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
        
        // Update local state
        const index = settings.value.findIndex(s => s.id === setting.id);
        if (index !== -1) {
          settings.value[index] = response.data;
        }
      } else {
        // Create new setting
        const response = await axios.post(
          '/appbAXrcL96xQIa1C/tblPd2cMBzVFBNvSl',
          {
            fields: {
              'Setting name': settingName,
              'Option 1': value
            }
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
        
        settings.value.push(response.data);
      }
    } catch (err) {
      console.error('Error updating setting:', err);
      throw err;
    }
  };

  return {
    settings,
    isLoading,
    error,
    fetchSettings,
    getSetting,
    updateSetting
  };
});