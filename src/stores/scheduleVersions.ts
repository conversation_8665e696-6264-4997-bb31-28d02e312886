import { defineStore } from 'pinia';
import { ref } from 'vue';
import axios from 'axios';

interface ScheduleVersion {
  id: string;
  fields: {
    'Display name'?: string;
    'Staff': string[];
    'Staff Member (from Staff)'?: string[];
    'Effective Date': string;
    'Working Hours': string;
    'Created Date'?: string;
    'Created By'?: any;
    'Notes'?: string;
  };
}

export const useScheduleVersionsStore = defineStore('scheduleVersions', () => {
  const scheduleVersions = ref<ScheduleVersion[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const fetchScheduleVersions = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const response = await axios.get(
        '/appbAXrcL96xQIa1C/tblZ6n1zdUe0ChLYx'
      );
      
      scheduleVersions.value = response.data.records || [];
    } catch (err) {
      console.error('Error fetching schedule versions:', err);
      error.value = 'Failed to fetch schedule versions';
    } finally {
      isLoading.value = false;
    }
  };

  const getScheduleForDate = (staffId: string, date: Date): string | null => {
    // Filter versions for this staff member
    const staffVersions = scheduleVersions.value.filter(
      version => version.fields.Staff.includes(staffId)
    );
    
    if (staffVersions.length === 0) return null;
    
    // Sort by effective date descending
    const sortedVersions = staffVersions.sort((a, b) => {
      const dateA = new Date(a.fields['Effective Date']);
      const dateB = new Date(b.fields['Effective Date']);
      return dateB.getTime() - dateA.getTime();
    });
    
    // Find the most recent version that's effective on or before the given date
    for (const version of sortedVersions) {
      const effectiveDate = new Date(version.fields['Effective Date']);
      if (date >= effectiveDate) {
        return version.fields['Working Hours'];
      }
    }
    
    return null;
  };

  const parseWorkingHours = (workingHoursText: string): Record<string, { start: number; end: number }> => {
    const schedule: Record<string, { start: number; end: number }> = {};
    
    if (!workingHoursText) return schedule;
    
    const lines = workingHoursText.split('\n');
    for (const line of lines) {
      const match = line.match(/^(\w+):\s*(\d{1,2}:\d{2}\s*[AP]M)\s*-\s*(\d{1,2}:\d{2}\s*[AP]M)/i);
      if (match) {
        const [, day, startTime, endTime] = match;
        if (day && startTime && endTime) {
          const start = parseTimeToDecimal(startTime);
          const end = parseTimeToDecimal(endTime);
          schedule[day] = { start, end };
        }
      }
    }
    
    return schedule;
  };

  const parseTimeToDecimal = (timeStr: string): number => {
    const parts = timeStr.trim().split(/\s+/);
    if (parts.length < 2) return 0;
    
    const [time, period] = parts;
    const timeParts = time?.split(':');
    if (!timeParts || timeParts.length < 2) return 0;
    
    const hours = parseInt(timeParts[0] || '0', 10);
    const minutes = parseInt(timeParts[1] || '0', 10);
    
    if (isNaN(hours) || isNaN(minutes)) return 0;
    
    let decimalHours = hours + (minutes / 60);
    
    if (period && period.toUpperCase() === 'PM' && hours !== 12) {
      decimalHours += 12;
    } else if (period && period.toUpperCase() === 'AM' && hours === 12) {
      decimalHours = minutes / 60;
    }
    
    return decimalHours;
  };

  return {
    scheduleVersions,
    isLoading,
    error,
    fetchScheduleVersions,
    getScheduleForDate,
    parseWorkingHours
  };
});