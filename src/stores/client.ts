import { defineStore, acceptHMRUpdate } from 'pinia'
import { CLIENT_TABLE_ID, CLIENT_VIEW_ID, MAIN_BASE_ID } from 'src/components/variable'
import { useScheduleStore } from './schedule.js'
import axios from 'axios'
import type { ClientInterface } from 'src/components/models'
import { getLastedDate } from 'src/utils'

const getAll = async (params: { view: string; filterByFormula: string }) => {
  let offset: string | undefined
  let clients: ClientInterface[] = []
  do {
    try {
      const paramsWithOffset = offset ? { ...params, offset } : params
      const response = await axios.get(`/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}`, {
        params: paramsWithOffset,
      })
      const records = response.data.records as ClientInterface[]
      clients = clients.concat(records)
      offset = response.data.offset
    } catch (err) {
      console.error(err)
      break
    }
  } while (offset)
  return clients
}
export const useClientStore = defineStore('client', {
  state: () => ({
    clients: [] as ClientInterface[],
    loading: false,
  }),

  getters: {
    _clients: (state) => state.clients,
  },

  actions: {
    async createClient(clientData: Record<string, any>) {
      // Ensure First Name and Last Name are provided (Patient Name is auto-computed)
      if (!clientData['First Name'] || !clientData['First Name'].trim()) {
        throw new Error('First name is required')
      }
      if (!clientData['Last Name'] || !clientData['Last Name'].trim()) {
        throw new Error('Last name is required')
      }

      // Clean up the data - remove empty strings and undefined values
      const fields: Record<string, any> = {}

      // List of auto-computed fields that should not be sent to the API
      const autoComputedFields = [
        'Patient Name', 'Client ID', 'Massages this Year', 'Last Visit Date',
        'Days Since Last Visit', 'Status (from Scripts)', 'Script PDF',
        'Visits This Year', 'Last Modified Time', 'Next Nerve Block - Suprascapular',
        'Next Nerve Block - Upper Back', 'Next Nerve Block - Mid Back',
        'Next Nerve Block - Lower Back', 'Next Nerve Block - Glutes',
        'Next Nerve Block - Bilateral Shoulders', 'Next Nerve Block - Sciatic Nerve',
        'Next Trigger Point - Neck', 'Next Trigger Point - Upper Back',
        'Next Trigger Point - Mid Back', 'Next Trigger Point - Lower Back',
        'Next Trigger Point - Glutes', 'Next Trigger Point - Bilateral Shoulders',
        'Next Trigger Point - Sciatic Nerve', 'Latest Script Expiration',
        'Latest Script Status', 'Latest Script Summary', 'RX Summary',
        'Last Modified 2', 'Client Record ID', 'Has Future Appointment(s) (from Schedule)',
        'Nerve Block Schedule', 'Created', 'Visits in 90 Days'
      ]

      Object.keys(clientData).forEach(key => {
        // Skip auto-computed fields
        if (autoComputedFields.includes(key)) {
          return
        }

        const value = clientData[key]
        if (value !== undefined && value !== null && value !== '') {
          if (typeof value === 'string') {
            const trimmed = value.trim()
            if (trimmed) {
              fields[key] = trimmed
            }
          } else {
            fields[key] = value
          }
        }
      })

      try {
        const response = await axios.post(
          `/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}`,
          {
            records: [{ fields }],
            typecast: true,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        )

        // The API returns an array of records, we want the first one
        const newClient = response.data.records[0]
        this.clients.push(newClient)
        return newClient
      } catch (error) {
        console.error('Failed to create client:', error)
        throw error
      }
    },
    async updateClient(id: string, name: string) {
      const scheduleStore = useScheduleStore()
      const res = await axios.patch(
        `/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${id}`,
        {
          fields: {
            'Patient Name': name,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: CLIENT_VIEW_ID,
          },
        },
      )
      try {
        const newData = res.data
        this.clients = this.clients.map((el) => {
          if (el.id === newData.id) return { ...newData }
          return el
        })
        const name = newData.fields['Patient Name']
        scheduleStore.changePatient(id, name)
      } catch {
        console.error('No credentials')
      }
    },
    async getUpdates() {
      if (this.loading) return
      this.loading = true
      const latestTime = getLastedDate(this.clients.map((el) => el.fields['Last Modified Time']))
      const params = {
        view: CLIENT_VIEW_ID,
        filterByFormula: `AND({Patient Name} != '', IS_AFTER({Last Modified Time}, '${latestTime}'))`,
      }
      try {
        let records = await getAll(params)
        records = records.filter((el) => el.fields['Patient Name'])
        if (records.length > 0) {
          const clientIds = this.clients.map((el) => el.id)
          const newRecords = records.filter((el) => !clientIds.includes(el.id))
          this.clients = this.clients
            .map((el) => {
              const found = records.find((record) => record.id === el.id)
              if (found) return { ...found }
              return el
            })
            .concat(newRecords)
        }
      } catch {
        /** */
      }
      this.loading = false
    },
    async getClients() {
      const params = {
        view: CLIENT_VIEW_ID,
        filterByFormula: `{Patient Name} != ''`,
      }
      let records = await getAll(params)
      records = records.filter((el) => el.fields['Patient Name'].trim())
      this.clients = records
    },
    async updateClientBalance(clientId: string, amount: number, note?: string) {
      try {
        const client = this.clients.find(c => c.id === clientId)
        if (!client) {
          throw new Error('Client not found')
        }

        const currentBalance = client.fields.Balance || 0
        const newBalance = currentBalance + amount

        const updateFields: Record<string, any> = {
          Balance: newBalance
        }

        if (note) {
          const existingNotes = client.fields['Alert Notes'] || ''
          const timestamp = new Date().toLocaleDateString()
          const newNote = `${timestamp}: ${note}`
          updateFields['Alert Notes'] = existingNotes 
            ? `${existingNotes}\n${newNote}`
            : newNote
        }

        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${clientId}`,
          {
            fields: updateFields,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: CLIENT_VIEW_ID,
            },
          },
        )

        const updatedClient = response.data
        this.clients = this.clients.map((el) => {
          if (el.id === clientId) return { ...updatedClient }
          return el
        })

        return updatedClient
      } catch (error) {
        console.error('Failed to update client balance:', error)
        throw error
      }
    },
    async addBalanceNote(clientId: string, note: string) {
      try {
        const client = this.clients.find(c => c.id === clientId)
        if (!client) {
          throw new Error('Client not found')
        }

        const existingNotes = client.fields['Alert Notes'] || ''
        const timestamp = new Date().toLocaleDateString()
        const newNote = `${timestamp}: ${note}`
        const updatedNotes = existingNotes 
          ? `${existingNotes}\n${newNote}`
          : newNote

        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${clientId}`,
          {
            fields: {
              'Alert Notes': updatedNotes
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: CLIENT_VIEW_ID,
            },
          },
        )

        const updatedClient = response.data
        this.clients = this.clients.map((el) => {
          if (el.id === clientId) return { ...updatedClient }
          return el
        })

        return updatedClient
      } catch (error) {
        console.error('Failed to add balance note:', error)
        throw error
      }
    },
    getClientBalance(clientId: string): number {
      const client = this.clients.find(c => c.id === clientId)
      return client?.fields.Balance || 0
    },
    hasBalanceAlert(clientId: string): boolean {
      const balance = this.getClientBalance(clientId)
      return balance > 0
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useClientStore, import.meta.hot))
}
