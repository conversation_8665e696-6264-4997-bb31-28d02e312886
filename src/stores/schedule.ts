import { defineStore, acceptHMRUpdate } from 'pinia'
import axios from 'axios'
import {
  MAIN_BASE_ID,
  SCHEDULE_TABLE_ID,
  SCHEDULE_VIEW_ID,
  US_TIME_FORMAT,
} from '../components/variable'
import type { ProviderField, ScheduleInterface, StatusType } from 'src/components/models'
import { getLastedDate, shouldPromptForFee } from 'src/utils'
import { useClientStore } from './client'
import { useFilterStore } from './filter'
import { useAuthStore } from './user'
import { httpsCallable, getFunctions } from 'firebase/functions'
import type { Moment } from 'moment'
import moment from 'moment'

const getAll = async (params: { view: string; filterByFormula: string }) => {
  let offset: string | undefined
  let schedules: ScheduleInterface[] = []
  do {
    try {
      const paramsWithOffset = offset ? { ...params, offset } : params
      const response = await axios.get(`/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}`, {
        params: paramsWithOffset,
      })
      const records = response.data.records as ScheduleInterface[]
      schedules = schedules.concat(records)
      offset = response.data.offset
    } catch (err) {
      console.error(err)
      break
    }
  } while (offset)
  return schedules
}
export const useScheduleStore = defineStore('schedule', {
  state: () => ({
    schedules: [] as ScheduleInterface[],
    //   records: [] as ScheduleInterface[],
    loading: false,
  }),

  getters: {},

  actions: {
    init() {
      this.schedules = []
    },
    async getUpdates() {
      if (this.loading) return
      const userStore = useAuthStore()
      this.loading = true
      const latestTime = getLastedDate(this.schedules.map((el) => el.fields['Last Modified']))
      const query = userStore.getQuery()
      let params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status != 'Archived', IS_AFTER({Last Modified}, '${latestTime}'))`,
      }
      if (query) {
        params = {
          ...params,
          filterByFormula: `AND(Status != 'Archived', IS_AFTER({Last Modified}, '${latestTime}'), ${query})`,
        }
      }
      try {
        const records = await getAll(params)
        if (records.length > 0) {
          const scheduleIds = this.schedules.map((el) => el.id)
          const newRecords = records.filter((el) => !scheduleIds.includes(el.id))
          this.schedules = this.schedules
            .map((el) => {
              const found = records.find((record) => record.id === el.id)
              if (found) return { ...found }
              return el
            })
            .concat(newRecords)
        }
      } catch {
        /** */
      }
      this.loading = false
    },
    async getSchedules() {
      const filterStore = useFilterStore()
      const userStore = useAuthStore()
      const { _date } = filterStore
      const month = moment(_date, US_TIME_FORMAT).month() + 1
      const year = moment(_date, US_TIME_FORMAT).year()
      const query = userStore.getQuery()
      let params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status != 'Archived', MONTH(Date) = ${month}, YEAR(Date) = ${year})`,
      }
      if (query) {
        params = {
          ...params,
          filterByFormula: `AND(Status != 'Archived', MONTH(Date) = ${month}, YEAR(Date) = ${year}, ${query})`,
        }
      }
      try {
        const records = await getAll(params)
        if (records.length > 0) {
          const newRecords = records.filter(
            (record) => !this.schedules.some((el) => el.id === record.id),
          )
          this.schedules = this.schedules
            .map((schedule) => {
              const record = records.find((record) => record.id === schedule.id)
              if (!record) return schedule
              return record
            })
            .concat(newRecords)
          this.schedules = this.schedules.filter((el) => el.fields.Status !== 'Archived')
        }
      } catch {
        /* */
      }
    },
    async coverSchedule(pid: string, id: string) {
      if (pid && id) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schedule: any = {
          Provider: [pid],
        }
        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${id}`,
          {
            fields: {
              ...schedule,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: SCHEDULE_VIEW_ID,
            },
          },
        )
        const newSchedule = response.data as ScheduleInterface
        this.schedules = this.schedules.map((el) => {
          if (el.id === id) return newSchedule
          return el
        })
      }
    },
    findPatientLastAppointment(patientId: string, excludeCurrentDate?: string): ScheduleInterface | null {
      if (!patientId) return null
      
      const patientAppointments = this.schedules.filter(schedule => {
        // Only include appointments (not time off)
        if (schedule.fields['Event Type'] !== 'Appointment') return false
        
        // Check if same patient
        if (!schedule.fields['Patient Name']?.[0] || schedule.fields['Patient Name'][0] !== patientId) return false
        
        // Exclude cancelled and archived appointments
        if (schedule.fields.Status === 'Cancelled' || schedule.fields.Status === 'Archived') return false
        
        // Optionally exclude appointments on the current date being scheduled
        if (excludeCurrentDate) {
          const appointmentDate = moment(schedule.fields.Date).format('YYYY-MM-DD')
          const excludeDate = moment(excludeCurrentDate).format('YYYY-MM-DD')
          if (appointmentDate === excludeDate) return false
        }
        
        return true
      })
      
      if (patientAppointments.length === 0) return null
      
      // Sort by date descending and return the most recent
      patientAppointments.sort((a, b) => moment(b.fields.Date).valueOf() - moment(a.fields.Date).valueOf())
      return patientAppointments[0] || null
    },

    findSameDayAppointments(patientId: string, appointmentDate: string, excludeId?: string): ScheduleInterface[] {
      const targetDate = moment(appointmentDate).format('YYYY-MM-DD')
      
      return this.schedules.filter(schedule => {
        // Skip the appointment being excluded (usually the one we're linking to others)
        if (excludeId && schedule.id === excludeId) return false
        
        // Only include appointments (not time off)
        if (schedule.fields['Event Type'] !== 'Appointment') return false
        
        // Check if same patient
        if (!schedule.fields['Patient Name']?.[0] || schedule.fields['Patient Name'][0] !== patientId) return false
        
        // Check if same date
        const scheduleDate = moment(schedule.fields.Date).format('YYYY-MM-DD')
        return scheduleDate === targetDate
      })
    },

    async linkAppointmentsTogether(appointmentIds: string[]) {
      if (appointmentIds.length < 2) return
      
      // Update each appointment to include all other appointment IDs in its Linked Appointments field
      for (const appointmentId of appointmentIds) {
        const linkedIds = appointmentIds.filter(id => id !== appointmentId)
        
        try {
          
          await axios.patch(
            `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${appointmentId}`,
            {
              fields: {
                'Related Appointments': linkedIds,
              },
            },
            {
              headers: {
                'Content-Type': 'application/json',
              },
              params: {
                view: SCHEDULE_VIEW_ID,
              },
            },
          )
          
          // Update local state
          this.schedules = this.schedules.map((schedule) => {
            if (schedule.id === appointmentId) {
              return {
                ...schedule,
                fields: {
                  ...schedule.fields,
                  'Related Appointments': linkedIds,
                }
              }
            }
            return schedule
          })
        } catch (error: any) {
          console.error(`Failed to link appointment ${appointmentId}:`, error)
        }
      }
    },

    async autoLinkSameDayAppointments(newAppointmentId: string, patientId: string, appointmentDate: string) {
      // Find all appointments for the same patient on the same day
      const sameDayAppointments = this.findSameDayAppointments(patientId, appointmentDate, newAppointmentId)
      
      if (sameDayAppointments.length === 0) return
      
      // Get all appointment IDs to link together (including the new one)
      const appointmentIds = [newAppointmentId, ...sameDayAppointments.map(apt => apt.id)]
      
      // Link all appointments together
      await this.linkAppointmentsTogether(appointmentIds)
      
    },

    async linkAllExistingSameDayAppointments(specificDate?: string) {
      const dateFilter = specificDate || null
      const dateLabel = dateFilter ? moment(dateFilter).format('MMMM D, YYYY') : 'all dates'
      
      
      // Group appointments by patient and date
      const appointmentGroups: { [key: string]: ScheduleInterface[] } = {}
      
      this.schedules.forEach(schedule => {
        // Only process appointments, not time off
        if (schedule.fields['Event Type'] !== 'Appointment') return
        if (!schedule.fields['Patient Name']?.[0]) return
        
        const date = moment(schedule.fields.Date).format('YYYY-MM-DD')
        
        // If specific date filter is provided, only process that date
        if (dateFilter && date !== moment(dateFilter).format('YYYY-MM-DD')) return
        
        const patientId = schedule.fields['Patient Name'][0]
        const key = `${patientId}_${date}`
        
        if (!appointmentGroups[key]) {
          appointmentGroups[key] = []
        }
        appointmentGroups[key].push(schedule)
      })
      
      // Link appointments in each group that has multiple appointments
      let linkedGroups = 0
      let totalAppointments = 0
      
      for (const [key, appointments] of Object.entries(appointmentGroups)) {
        if (appointments.length > 1) {
          const appointmentIds = appointments.map(apt => apt.id)
          await this.linkAppointmentsTogether(appointmentIds)
          linkedGroups++
          totalAppointments += appointments.length
          
        }
      }
      
      return { linkedGroups, totalAppointments, dateProcessed: dateLabel }
    },

    async syncLinkedAppointmentStatus(appointmentId: string, newStatus: StatusType) {
      // Don't sync if the status is Cancelled - let cancellations be independent
      if (newStatus === 'Cancelled') {
        return
      }

      const appointment = this.schedules.find(schedule => schedule.id === appointmentId)
      if (!appointment?.fields['Related Appointments']?.length) {
        return
      }

      // Get all linked appointment IDs (excluding the current one being updated)
      const linkedAppointmentIds = appointment.fields['Related Appointments'].filter(id => id !== appointmentId)
      
      // Update status for all linked appointments
      for (const linkedId of linkedAppointmentIds) {
        try {
          const response = await axios.patch(
            `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${linkedId}`,
            {
              fields: {
                Status: newStatus,
              },
            },
            {
              headers: {
                'Content-Type': 'application/json',
              },
              params: {
                view: SCHEDULE_VIEW_ID,
              },
            },
          )
          
          // Update the local schedule state
          const updatedSchedule = response.data as ScheduleInterface
          this.schedules = this.schedules.map((el) => {
            if (el.id === linkedId) {
              return updatedSchedule
            }
            return el
          })
        } catch (error) {
          console.error(`Failed to sync status for linked appointment ${linkedId}:`, error)
        }
      }
    },

    async updateSchedule(pid: string, field?: ProviderField, skipFeePrompt?: boolean) {
      if (field) {
        // Get the original schedule to check for status changes
        const originalSchedule = this.schedules.find(s => s.id === field.id)
        const originalStatus = originalSchedule?.fields.Status
        
        // Check if we should prompt for fee collection
        
        if (!skipFeePrompt && field.event === 'Appointment' && field.status && 
            shouldPromptForFee(String(field.start), field.status, originalStatus)) {
          
          // Get patient info for the fee prompt
          const patientId = originalSchedule?.fields['Patient Name']?.[0]
          if (patientId) {
            const clientStore = useClientStore()
            const patient = clientStore._clients.find(c => c.id === patientId)
            
            if (patient) {
              // Return a special object that indicates fee collection is needed
              return {
                needsFeeCollection: true,
                patientName: patient.fields['Patient Name'],
                patientId: patientId,
                status: field.status,
                field: field,
                pid: pid
              }
            }
          }
        }
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schedule: any = {
          'Appointment Type': field.appointment,
          Date: field.start as string,
          Duration: field.duration,
          Provider: [pid],
        }
        if (field.event === 'Appointment') {
          schedule['Appointment Type'] = field.appointment
          schedule['Notes'] = field.notes
          schedule['Status'] = field.status
        }
        const response = await axios.patch(
          `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${field.id}`,
          {
            fields: {
              ...schedule,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            params: {
              view: SCHEDULE_VIEW_ID,
            },
          },
        )
        const newSchedule = response.data as ScheduleInterface
        
        // Sync linked appointment statuses if status was updated
        if (field.event === 'Appointment' && field.status) {
          await this.syncLinkedAppointmentStatus(field.id, field.status)
        }
        
        if (newSchedule.fields['Status'] === 'Cancelled') {
          this.schedules = this.schedules.filter((el) => el.id !== newSchedule.id)
        } else {
          this.schedules = this.schedules.map((el) => {
            if (el.id === field.id) {
              return newSchedule
            }
            return el
          })
        }
      }
      this.loading = false
    },
    
    async updateScheduleWithFeeCollection(
      pid: string, 
      field: ProviderField, 
      feeResult?: { action: string; amount?: number; note?: string }
    ) {
      // First update the schedule
      await this.updateSchedule(pid, field, true) // Skip fee prompt since we already handled it
      
      // Then handle fee collection if requested
      if (feeResult?.action === 'add-fee' && feeResult.amount && feeResult.amount > 0) {
        const originalSchedule = this.schedules.find(s => s.id === field.id)
        const patientId = originalSchedule?.fields['Patient Name']?.[0]
        
        if (patientId) {
          const clientStore = useClientStore()
          try {
            await clientStore.updateClientBalance(
              patientId, 
              feeResult.amount, 
              feeResult.note || `${field.status} fee`
            )
          } catch (error) {
            console.error('Failed to update patient balance:', error)
            throw error
          }
        }
      }
    },
    async changePatient(id: string, name: string) {
      this.schedules = this.schedules.map((el) => {
        const patiendId = el.fields['Patient Name'][0]
        if (patiendId === id) {
          return {
            ...el,
            fields: {
              ...el.fields,
              'Patient Name (from Patient Name)': [name],
            },
          }
        }
        return el
      })
    },
    async createNewAppointement(providerId: string, field: ProviderField) {
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const schedule: any = {
          Date: field.start,
          Duration: field.duration,
          'Event Type': field.event,
          Provider: [providerId],
          Status: 'Pending',
        }
        if (field.event === 'Appointment') {
          schedule['Appointment Type'] = field.appointment
          schedule['Notes'] = field.notes
          schedule['Patient Name'] = field.patientId
          schedule['Status'] = field.status
        } else if (field.event === 'Time Off') {
          // For Time Off appointments, don't send Notes or Name fields
          // Airtable expects only: Date, Duration, Event Type, Provider, Status
        }
        
        const response = await axios.post(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}`,
        {
          fields: {
            ...schedule,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      this.schedules.push(response.data)
      
      // Auto-link appointments for the same patient on the same day
      if (field.event === 'Appointment' && field.patientId?.[0]) {
        await this.autoLinkSameDayAppointments(response.data.id, field.patientId[0], field.start as string)
      }
      
      // Send immediate booking confirmation SMS via Firebase Function
      if (field.event === 'Appointment') {
        try {
          const functions = getFunctions()
          const sendImmediateConfirmation = httpsCallable(functions, 'sendImmediateConfirmation')
          await sendImmediateConfirmation({ appointmentId: response.data.id })
        } catch (error) {
          console.warn('Failed to schedule booking confirmation SMS:', error)
        }
      }
      
      } catch (error: any) {
        console.error('Failed to create appointment:', error)
        throw error // Re-throw so the UI can handle it
      }
    },
    async deteleAppointment(id: string) {
      if (!id) return
      const schedule = {
        Status: 'Archived',
      }
      await axios.patch(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${id}`,
        {
          fields: {
            ...schedule,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      this.schedules = this.schedules.filter((el) => el.id !== id)
    },
    async getSchedulesFromDateRange(start: Date, end: Date) {
      const params = {
        view: SCHEDULE_VIEW_ID,
        filterByFormula: `AND(Status = 'Pending', IS_AFTER({Date}, '${start}'), IS_BEFORE({Date}, '${end}'))`,
      }
      try {
        const records = await getAll(params)
        return records
      } catch {
        /** */
      }
      return []
    },
    async setConfirm(recordId: string) {
      const response = await axios.patch(
        `/${MAIN_BASE_ID}/${SCHEDULE_TABLE_ID}/${recordId}`,
        {
          fields: {
            Status: 'Confirmed',
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            view: SCHEDULE_VIEW_ID,
          },
        },
      )
      const newSchedule = response.data as ScheduleInterface
      
      // Sync linked appointment statuses
      await this.syncLinkedAppointmentStatus(recordId, 'Confirmed')
      
      return newSchedule
    },
    hasSchedule(param: Moment) {
      const paramMonth = param.month() + 1
      const paramYear = param.year()
      return this.schedules.some((schedule) => {
        const scheduleM = moment.utc(schedule.fields.Date).subtract(4, 'hours')
        const year = scheduleM.year()
        const month = scheduleM.month() + 1
        return paramYear === year && paramMonth === month
      })
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useScheduleStore, import.meta.hot))
}
