import moment from 'moment'
import { WeekArray } from './components/variable'
import type { StatusType } from './components/models'

export const parseWorkingHours = (working: string) => {
  if (!working) return []
  try {
    const workingArr = working.split('\n')
    const arr = workingArr
      .map((el) => {
        const dayStr = el.trim()
        const [day, timeStr] = dayStr.split(': ')
        if (day && timeStr) {
          const [from, to] = timeStr.split(' - ')
          if (from && to) {
            return {
              day: WeekArray.findIndex((el) => el === day),
              from: moment(from, 'h:mm A').hours() + (moment(from, 'h:mm A').minutes() / 60),
              to: moment(to, 'h:mm A').hours() + (moment(to, 'h:mm A').minutes() / 60),
            }
          }
        }
        return null
      })
      .filter((el) => !!el)
    return arr || []
  } catch {
    /** */
  }
  return []
}
export const compareDate = (
  date: string | Date,
  selectedDate: Date,
  check: 'same' | 'after' | 'before',
) => {
  const momentA = moment(selectedDate)
  const momentB = moment(date, 'YYYY-MM-DD')
  if (check === 'same') return momentA.isSame(momentB, 'day')
  else if (check === 'after') return momentA.isAfter(momentB, 'day')
  return momentA.isBefore(momentB, 'day')
}
export const getLastedDate = (utcTimes: string[]) => {
  return utcTimes.reduce((latest, current) => {
    return new Date(current) > new Date(latest) ? current : latest
  })
}
export const generateTimeList = () => {
  const timeList = []
  let currentTime = moment() // Get current time using Moment.js
  if (currentTime.minutes() > 45) currentTime.minutes(45)
  else if (currentTime.minutes() > 30) currentTime.minutes(30)
  else if (currentTime.minutes() > 15) currentTime.minutes(15)
  else currentTime.minutes(0)
  // Generate time list every 15 minutes
  for (let i = 0; i < 24 * 4; i++) {
    // 24 hours * 4 (15-minute intervals)
    timeList.push(currentTime.format('hh:mm A')) // 12-hour format with AM/PM
    currentTime = currentTime.add(15, 'minutes') // Add 15 minutes to the current time
  }
  return {
    time: currentTime.format('hh:mm A'),
    options: timeList,
  }
}

export const isWithin48HoursStatusChange = (
  appointmentDate: string,
  newStatus: StatusType,
  previousStatus?: StatusType
): boolean => {
  if (!appointmentDate) return false
  
  const appointmentMoment = moment(appointmentDate)
  const now = moment()
  
  // Check if appointment is within 48 hours
  const hoursUntilAppointment = appointmentMoment.diff(now, 'hours')
  const isWithin48Hours = hoursUntilAppointment <= 48 && hoursUntilAppointment >= 0
  
  // Check if status is changing to No Show or Cancelled
  const isFeeableStatus = newStatus === 'No Show' || newStatus === 'Cancelled'
  
  // Check if this is actually a status change (not just updating the same status)
  const isStatusChange = Boolean(previousStatus && previousStatus !== newStatus)
  
  return isWithin48Hours && isFeeableStatus && isStatusChange
}

export const shouldPromptForFee = (
  appointmentDate: string,
  newStatus: StatusType,
  previousStatus?: StatusType
): boolean => {
  return isWithin48HoursStatusChange(appointmentDate, newStatus, previousStatus)
}

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}
