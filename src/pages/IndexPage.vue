<template>
  <q-page class="bg-grey-2">
    <template v-if="!loading">
      <admin-calendar />
      <!----
      <staff-calendar v-else />
    -->
    </template>
    <q-spinner v-else class="absolute-center q-mb-xl" style="top: 42%" color="primary" size="4em" />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useScheduleStore } from 'stores/schedule'
import { useStaffStore } from 'stores/staff'
import { useFilterStore } from 'stores/filter'
import { useClientStore } from 'stores/client'
import { useScheduleVersionsStore } from 'stores/scheduleVersions'
import { useSettingsStore } from 'stores/settings'
import { useTimer } from 'src/composables/useTimer'
import AdminCalendar from 'components/calendar/AdminCalendar.vue'
//import StaffCalendar from 'components/calendar/StaffCalendar.vue'
useTimer()
const scheduleStore = useScheduleStore()
const staffStore = useStaffStore()
const filterStore = useFilterStore()
const clientStore = useClientStore()
const scheduleVersionsStore = useScheduleVersionsStore()
const settingsStore = useSettingsStore()
const { loading } = storeToRefs(filterStore)
const { schedules } = scheduleStore
const { clients } = clientStore
const { staffs } = staffStore
onMounted(() => {
  if (schedules.length === 0 || clients.length === 0 || staffs.length === 0)
    filterStore.setLoading(true)
  Promise.all([
    scheduleStore.getSchedules(), 
    staffStore.getStaffs(), 
    clientStore.getClients(),
    scheduleVersionsStore.fetchScheduleVersions(),
    settingsStore.fetchSettings()
  ])
    .then(() => {
      filterStore.setLoading(false)
    })
    .catch(() => {
      filterStore.setLoading(false)
    })
})
</script>
