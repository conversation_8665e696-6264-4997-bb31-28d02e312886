<template>
  <q-dialog 
    v-model="isOpen" 
    :persistent="false"
    :position="position"
    :no-backdrop-dismiss="false"
    :no-backdrop="true"
    seamless
    @before-show="onBeforeShow"
    @hide="onHide"
  >
    <div
      ref="modalContainer"
      :class="[
        'draggable-modal-container',
        { minimized: isMinimized, maximized: isMaximized }
      ]"
      :style="modalContainerStyle"
    >
      <!-- Modal Header with drag handle and controls -->
      <div
        ref="dragHandle"
        class="draggable-modal-header"
        @mousedown="startDrag"
        @touchstart="startDrag"
      >
        <div class="header-content">
          <slot name="header">
            <div class="text-subtitle2 text-weight-medium">{{ title }}</div>
          </slot>
        </div>
        <div class="header-controls">
          <q-btn
            icon="minimize"
            flat
            round
            dense
            size="sm"
            @click="toggleMinimize"
            class="control-btn"
            :title="isMinimized ? 'Restore' : 'Minimize'"
          />
          <q-btn
            :icon="isMaximized ? 'fullscreen_exit' : 'fullscreen'"
            flat
            round
            dense
            size="sm"
            @click="toggleMaximize"
            class="control-btn"
            :title="isMaximized ? 'Restore' : 'Maximize'"
          />
          <q-btn
            icon="close"
            flat
            round
            dense
            size="sm"
            @click="close"
            class="control-btn close-btn"
            title="Close"
          />
        </div>
      </div>

      <!-- Modal Content -->
      <div 
        class="draggable-modal-content"
        :class="{ 'content-hidden': isMinimized }"
      >
        <slot></slot>
      </div>

      <!-- Resize handles -->
      <div v-if="resizable && !isMaximized" class="resize-handles">
        <div class="resize-handle resize-handle-se" @mousedown="startResize('se')"></div>
        <div class="resize-handle resize-handle-sw" @mousedown="startResize('sw')"></div>
        <div class="resize-handle resize-handle-ne" @mousedown="startResize('ne')"></div>
        <div class="resize-handle resize-handle-nw" @mousedown="startResize('nw')"></div>
        <div class="resize-handle resize-handle-n" @mousedown="startResize('n')"></div>
        <div class="resize-handle resize-handle-s" @mousedown="startResize('s')"></div>
        <div class="resize-handle resize-handle-e" @mousedown="startResize('e')"></div>
        <div class="resize-handle resize-handle-w" @mousedown="startResize('w')"></div>
      </div>
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, nextTick } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  position?: 'standard' | 'top' | 'right' | 'bottom' | 'left'
  resizable?: boolean
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
  initialWidth?: number
  initialHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  position: 'standard',
  resizable: true,
  minWidth: 300,
  minHeight: 200,
  maxWidth: 1200,
  maxHeight: 800,
  initialWidth: 500,
  initialHeight: 400
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// Reactive state
const modalContainer = ref<HTMLElement>()
const dragHandle = ref<HTMLElement>()
const isDragging = ref(false)
const isResizing = ref(false)
const isMinimized = ref(false)
const isMaximized = ref(false)
const resizeDirection = ref('')

// Position and size state
const modalState = ref({
  x: 0,
  y: 0,
  width: props.initialWidth,
  height: props.initialHeight,
  startX: 0,
  startY: 0,
  startWidth: 0,
  startHeight: 0
})

const savedState = ref({
  x: 0,
  y: 0,
  width: props.initialWidth,
  height: props.initialHeight
})

const isOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const modalContainerStyle = computed(() => {
  if (isMaximized.value) {
    return {
      position: 'fixed' as const,
      top: '0px',
      left: '0px',
      width: '100vw',
      height: '100vh',
      transform: 'none',
      zIndex: '6000'
    }
  }

  if (isMinimized.value) {
    return {
      position: 'fixed' as const,
      bottom: '20px',
      right: '20px',
      width: '300px',
      height: 'auto',
      transform: 'none',
      zIndex: '6000'
    }
  }

  return {
    position: 'fixed' as const,
    left: `${modalState.value.x}px`,
    top: `${modalState.value.y}px`,
    width: `${modalState.value.width}px`,
    height: `${modalState.value.height}px`,
    transform: 'none',
    zIndex: '6000'
  }
})

// Event handlers
const onBeforeShow = () => {
  nextTick(() => {
    centerModal()
  })
}

const onHide = () => {
  emit('close')
}

const centerModal = () => {
  if (modalContainer.value) {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    modalState.value.x = (viewportWidth - modalState.value.width) / 2
    modalState.value.y = (viewportHeight - modalState.value.height) / 2
    
    // Ensure modal stays within viewport
    modalState.value.x = Math.max(0, Math.min(modalState.value.x, viewportWidth - modalState.value.width))
    modalState.value.y = Math.max(0, Math.min(modalState.value.y, viewportHeight - modalState.value.height))
  }
}

const startDrag = (e: MouseEvent | TouchEvent) => {
  if (isMaximized.value || isMinimized.value) return
  
  isDragging.value = true
  const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
  const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY
  
  modalState.value.startX = clientX - modalState.value.x
  modalState.value.startY = clientY - modalState.value.y
  
  document.addEventListener('mousemove', drag)
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchmove', drag)
  document.addEventListener('touchend', stopDrag)
  
  e.preventDefault()
}

const drag = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return
  
  const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
  const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY
  
  modalState.value.x = clientX - modalState.value.startX
  modalState.value.y = clientY - modalState.value.startY
  
  // Keep modal within viewport
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  modalState.value.x = Math.max(0, Math.min(modalState.value.x, viewportWidth - modalState.value.width))
  modalState.value.y = Math.max(0, Math.min(modalState.value.y, viewportHeight - modalState.value.height))
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', drag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', drag)
  document.removeEventListener('touchend', stopDrag)
}

const startResize = (direction: string) => {
  if (isMaximized.value) return
  
  isResizing.value = true
  resizeDirection.value = direction
  
  modalState.value.startX = modalState.value.x
  modalState.value.startY = modalState.value.y
  modalState.value.startWidth = modalState.value.width
  modalState.value.startHeight = modalState.value.height
  
  document.addEventListener('mousemove', resize)
  document.addEventListener('mouseup', stopResize)
}

const resize = (e: MouseEvent) => {
  if (!isResizing.value) return
  
  const deltaX = e.clientX - modalState.value.startX
  const deltaY = e.clientY - modalState.value.startY
  
  const direction = resizeDirection.value
  
  let newWidth = modalState.value.width
  let newHeight = modalState.value.height
  let newX = modalState.value.x
  let newY = modalState.value.y
  
  if (direction.includes('e')) {
    newWidth = Math.max(props.minWidth, Math.min(props.maxWidth, modalState.value.startWidth + deltaX))
  }
  if (direction.includes('w')) {
    newWidth = Math.max(props.minWidth, Math.min(props.maxWidth, modalState.value.startWidth - deltaX))
    newX = modalState.value.startX + (modalState.value.startWidth - newWidth)
  }
  if (direction.includes('s')) {
    newHeight = Math.max(props.minHeight, Math.min(props.maxHeight, modalState.value.startHeight + deltaY))
  }
  if (direction.includes('n')) {
    newHeight = Math.max(props.minHeight, Math.min(props.maxHeight, modalState.value.startHeight - deltaY))
    newY = modalState.value.startY + (modalState.value.startHeight - newHeight)
  }
  
  modalState.value.width = newWidth
  modalState.value.height = newHeight
  modalState.value.x = newX
  modalState.value.y = newY
}

const stopResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', resize)
  document.removeEventListener('mouseup', stopResize)
}

const toggleMinimize = () => {
  if (isMinimized.value) {
    // Restore from minimized
    isMinimized.value = false
    modalState.value = { ...savedState.value }
  } else {
    // Save current state and minimize
    if (!isMaximized.value) {
      savedState.value = { ...modalState.value }
    }
    isMinimized.value = true
    isMaximized.value = false
  }
}

const toggleMaximize = () => {
  if (isMaximized.value) {
    // Restore from maximized
    isMaximized.value = false
    modalState.value = { ...savedState.value }
  } else {
    // Save current state and maximize
    if (!isMinimized.value) {
      savedState.value = { ...modalState.value }
    }
    isMaximized.value = true
    isMinimized.value = false
  }
}

const close = () => {
  isOpen.value = false
}

// Cleanup on unmount
onUnmounted(() => {
  document.removeEventListener('mousemove', drag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', drag)
  document.removeEventListener('touchend', stopDrag)
  document.removeEventListener('mousemove', resize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style lang="scss" scoped>
.draggable-modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
  pointer-events: auto;
  display: flex;
  flex-direction: column;
  max-height: 100vh; // Prevent modal from exceeding viewport height
  
  &.minimized {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    
    .draggable-modal-header {
      border-radius: 8px;
    }
  }
  
  &.maximized {
    border-radius: 0;
    box-shadow: none;
    max-height: 100vh;
  }
}

.draggable-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: move;
  user-select: none;
  
  .header-content {
    flex: 1;
    color: #333;
  }
  
  .header-controls {
    display: flex;
    gap: 4px;
    
    .control-btn {
      min-width: 32px;
      min-height: 32px;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
      
      &.close-btn:hover {
        background: #ff5252;
        color: white;
      }
    }
  }
}

.draggable-modal-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
  min-height: 0; // Important for flex child to shrink
  
  &.content-hidden {
    display: none;
  }
  
  // Ensure proper scrolling behavior
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.resize-handles {
  .resize-handle {
    position: absolute;
    background: transparent;
    
    &.resize-handle-se {
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      cursor: se-resize;
    }
    
    &.resize-handle-sw {
      bottom: 0;
      left: 0;
      width: 12px;
      height: 12px;
      cursor: sw-resize;
    }
    
    &.resize-handle-ne {
      top: 0;
      right: 0;
      width: 12px;
      height: 12px;
      cursor: ne-resize;
    }
    
    &.resize-handle-nw {
      top: 0;
      left: 0;
      width: 12px;
      height: 12px;
      cursor: nw-resize;
    }
    
    &.resize-handle-n {
      top: 0;
      left: 12px;
      right: 12px;
      height: 4px;
      cursor: n-resize;
    }
    
    &.resize-handle-s {
      bottom: 0;
      left: 12px;
      right: 12px;
      height: 4px;
      cursor: s-resize;
    }
    
    &.resize-handle-e {
      right: 0;
      top: 12px;
      bottom: 12px;
      width: 4px;
      cursor: e-resize;
    }
    
    &.resize-handle-w {
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 4px;
      cursor: w-resize;
    }
    
    &:hover {
      background: rgba(25, 118, 210, 0.2);
    }
  }
}

// Ensure modal appears above everything but doesn't block background
:deep(.q-dialog__inner) {
  padding: 0;
  pointer-events: none;
}

// Mobile responsive styles
@media (max-width: 480px) {
  .draggable-modal-container {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 !important;
  }
}

// Override Quasar's default dialog positioning
:deep(.q-dialog) {
  .draggable-modal-container {
    pointer-events: auto;
  }
}
</style>