<template>
  <div class="bg-white calendar-wrapper" style="border-top-left-radius: 20px">
    <!-- Desktop toolbar - only visible on desktop -->
    <div class="desktop-toolbar gt-sm">
      <q-btn
        outline
        class="q-px-xl"
        color="grey-9"
        label="Today"
        rounded
        @click="onToday"
        no-caps
      />
      <q-btn
        icon="arrow_back_ios"
        dense
        class="q-ml-xl"
        color="grey-9"
        flat
        round
        :ripple="false"
        @click="onPrev"
      />
      <q-btn
        class="q-ml-sm"
        icon="arrow_forward_ios"
        color="grey-9"
        flat
        round
        :ripple="false"
        dense
        @click="onNext"
      />
      <div class="text-grey-9 q-ml-md text-h6">{{ formattedSelectedDate }}</div>
      <q-space />
      <q-input
        dense
        filled
        v-model="patientName"
        placeholder="Search by patient"
        unelevated
        no-caps
        class="toolbar-search"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

    </div>

    <!-- Mobile date header - only visible on mobile -->
    <div class="mobile-date-header lt-md">
      <div class="mobile-header-top">
        <div class="current-date-display">
          {{ currentDateFormatted }}
          <q-btn
            outline
            size="sm"
            color="primary"
            label="Today"
            rounded
            @click="onToday"
            no-caps
            class="mobile-today-btn"
          />
        </div>
      </div>
      <div class="mobile-date-switcher">
        <div class="date-switcher-container">
          <q-btn
            icon="chevron_left"
            flat
            round
            size="sm"
            color="grey-7"
            @click="onPrevWeek"
            class="nav-btn"
          />
          <div class="week-dates">
            <div
              v-for="date in weekDates"
              :key="date.value"
              :class="['date-item', { active: date.isSelected, today: date.isToday }]"
              @click="onDateSelect(date.value)"
            >
              <div class="day-name">{{ date.dayName }}</div>
              <div class="day-number">{{ date.dayNumber }}</div>
            </div>
          </div>
          <q-btn
            icon="chevron_right"
            flat
            round
            size="sm"
            color="grey-7"
            @click="onNextWeek"
            class="nav-btn"
          />
        </div>
      </div>
    </div>

    <!-- Desktop header - hidden on mobile -->
    <div class="mmm-calendar-header row q-pl-lg items-center text-grey-9 gt-sm">
      <q-icon name="event_available" size="1.7em" />
      <div class="text-h6 q-ml-xs">{{ realEvents.length }}</div>
      <div class="text-body2 q-ml-xs">Total Appointments</div>
    </div>

    <!-- Calendar container with mobile scrolling -->
    <div
      class="calendar-container"
      :style="{
        '--zoom-level': zoomLevel,
      }"
    >
      <div
        class="calendar-inner-wrapper"
        :style="{
          minHeight: `${dynamicContainerHeight}px`,
          height: 'auto',
        }"
      >
        <vue-cal
          :disable-views="['years', 'year', 'month']"
          active-view="day"
          :split-days="providers"
          :events="events"
          sticky-split-labels
          :time-from="timeRange.min"
          :time-step="30"
          :time-to="timeRange.max"
          :selected-date="selectedDate"
          time-format="hh:mm AM"
          :watch-real-time="true"
          :time-cell-height="scaledTimeHeight"
          :min-cell-width="scaledMinWidth"
          :min-split-width="scaledMinWidth"
          :style="{ fontSize: `${scaledFontSize}rem` }"
        >
          <template #split-label="{ split }">
            <div class="full-height relative-position full-width">
              <div class="absolute-center text-grey-9 full-width">
                <div style="width: fit-content; margin: auto">
                  <div class="gt-md text-body1">
                    <strong :style="`color: ${split.color}`">{{ split.label }}</strong>
                  </div>
                  <div class="lt-lg text-body2">
                    <strong :style="`color: ${split.color}`">{{ split.label }}</strong>
                  </div>
                  <div class="text-caption gt-md mmm-split-description">
                    Appointments: {{ splitLen(split.id) }}
                  </div>
                  <div class="text-caption lt-lg mmm-split-description text-center">
                    ({{ splitLen(split.id) }})
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template #event="{ event }">
            <event-component v-bind="event" />
          </template>
          <template #cell-content="{ split }">
            <div @click="onCellClicked" class="highlight-cell"></div>
            <div class="highlight-timeframe"></div>
          </template>
          <template #time-cell="{ hours, minutes }">
            <div
              :class="{ 'vuecal__time-cell-line': true, hours: !minutes }"
              style="font-size: 0.6rem"
            >
              {{ moment(`${hours}:${minutes}`, 'H:m').format('hh:mm A') }}
            </div>
          </template>
        </vue-cal>
      </div>
    </div>
    <draggable-modal 
      v-model="appointment" 
      title="Appointment Details"
      :initial-width="500"
      :initial-height="730"
      @close="appointment = false"
    >
      <appointment-modal v-bind="providerField" @close="appointment = false" />
    </draggable-modal>

    <!-- Floating zoom controls -->
    <div class="floating-zoom-controls gt-sm">
      <q-btn
        icon="zoom_out"
        flat
        round
        size="md"
        color="white"
        @click="zoomOut"
        :disable="zoomLevel <= 0.5"
        class="zoom-btn"
      />
      <div class="zoom-level-display">{{ Math.round(zoomLevel * 100) }}%</div>
      <q-btn
        icon="zoom_in"
        flat
        round
        size="md"
        color="white"
        @click="zoomIn"
        :disable="zoomLevel >= 2"
        class="zoom-btn"
      />
      <q-btn
        icon="center_focus_strong"
        flat
        round
        size="sm"
        color="white"
        @click="resetZoom"
        class="zoom-btn reset-btn"
        v-if="zoomLevel !== 1"
      />
    </div>

    <!-- Mobile zoom controls in header -->
    <div class="mobile-zoom-controls lt-md">
      <q-btn
        icon="zoom_out"
        flat
        round
        size="sm"
        color="grey-7"
        @click="zoomOut"
        :disable="zoomLevel <= 0.5"
        class="mobile-zoom-btn"
      />
      <div class="mobile-zoom-level">{{ Math.round(zoomLevel * 100) }}%</div>
      <q-btn
        icon="zoom_in"
        flat
        round
        size="sm"
        color="grey-7"
        @click="zoomIn"
        :disable="zoomLevel >= 2"
        class="mobile-zoom-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch, ref } from 'vue'
import moment from 'moment'
import VueCal from 'vue-cal'
import EventComponent from 'components/event/EventComponent.vue'
import 'vue-cal/dist/vuecal.css'
import { useEvent } from 'src/composables/useEvent'
import { DEFAULT_PROVIDER_FIELD, US_TIME_FORMAT } from 'components/variable.js'
import AppointmentModal from 'components/event/AppointmentModal.vue'
import DraggableModal from 'components/common/DraggableModal.vue'
import type { AppointmentType, ProviderField } from '../models'
import { useFilterStore } from 'src/stores/filter'
import { useScheduleStore } from 'src/stores/schedule'

const minWidth = ref(180)
const appointment = ref(false)
const zoomLevel = ref(1) // Zoom level from 0.5x to 2.0x
const { selectedDate, timeRange, events, providers } = useEvent()
const filterStore = useFilterStore()
const scheduleStore = useScheduleStore()

// Computed properties for scaled dimensions
const scaledTimeHeight = computed(() => {
  const baseHeight = timeRange.value.height || 50
  return Math.round(baseHeight * zoomLevel.value)
})

const scaledMinWidth = computed(() => {
  return Math.round(minWidth.value * zoomLevel.value)
})

const scaledFontSize = computed(() => {
  return Math.max(0.5, Math.min(1.5, zoomLevel.value)) // Keep font scaling reasonable
})

// Calculate dynamic container height to accommodate zoomed content
const dynamicContainerHeight = computed(() => {
  const timeSlots = (timeRange.value.max - timeRange.value.min) / 30 // 30-minute intervals
  const totalContentHeight = timeSlots * scaledTimeHeight.value
  const headerHeight = 48 // Split headers height
  const minHeight = 400 // Minimum height to ensure usability
  return Math.max(minHeight, totalContentHeight + headerHeight + 100) // Add padding
})
const realEvents = computed(() =>
  events.value.filter(
    (el) =>
      !el.non_event &&
      el.event === 'Appointment' &&
      el.status !== 'Cancelled' &&
      el.status !== 'No Show',
  ),
)
const providerField = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)
const splitLen = computed(
  () => (id: number) => realEvents.value.filter((el) => el.split === id).length,
)

// Toolbar functionality
const formattedSelectedDate = computed(() =>
  moment(filterStore._date, US_TIME_FORMAT).format('dddd, MMMM D, YYYY'),
)
const patientName = computed({
  get: () => filterStore._patientName,
  set: (val) => filterStore.changePatient(val),
})

const onToday = () => {
  const date = moment().format(US_TIME_FORMAT)
  filterStore.changeDate(date)
}

const onPrev = async () => {
  const prevMoment = moment(filterStore._date, US_TIME_FORMAT).subtract(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(prevMoment)
  filterStore.changeDate(prevMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onNext = async () => {
  const nextMoment = moment(filterStore._date, US_TIME_FORMAT).add(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(nextMoment)
  filterStore.changeDate(nextMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}


// Mobile calendar functionality
const currentDateFormatted = computed(() => {
  return moment(selectedDate.value).format('dddd, MMMM D, YYYY')
})

const weekDates = computed(() => {
  const startOfWeek = moment(selectedDate.value).startOf('week')
  const dates = []

  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.clone().add(i, 'days')
    dates.push({
      value: date.format(US_TIME_FORMAT),
      dayName: date.format('ddd'),
      dayNumber: date.format('D'),
      isSelected: date.isSame(selectedDate.value, 'day'),
      isToday: date.isSame(moment(), 'day'),
    })
  }

  return dates
})

// Mobile navigation methods
const onDateSelect = async (dateValue: string) => {
  const hasSchedule = scheduleStore.hasSchedule(moment(dateValue, US_TIME_FORMAT))
  filterStore.changeDate(dateValue)
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onPrevWeek = async () => {
  const prevWeek = moment(selectedDate.value).subtract(1, 'week')
  const hasSchedule = scheduleStore.hasSchedule(prevWeek)
  filterStore.changeDate(prevWeek.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onNextWeek = async () => {
  const nextWeek = moment(selectedDate.value).add(1, 'week')
  const hasSchedule = scheduleStore.hasSchedule(nextWeek)
  filterStore.changeDate(nextWeek.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

// Zoom functions
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

onMounted(() => {
  setHighlight()
})
const isEmptyInArea = (elements: NodeListOf<Element>, hoverTop: number) => {
  for (let i = 0; i < elements.length; i++) {
    const element = elements[i] as HTMLElement
    try {
      const hoverHeight = scaledTimeHeight.value || 0
      const elementTop = parseInt(element.style.top)
      const elementHeight = parseInt(element.style.height)
      const bottom = Math.min(elementTop + elementHeight, hoverTop + hoverHeight)
      const top = Math.max(elementTop, hoverTop)
      if (bottom > top) return false
    } catch {
      /** */
    }
  }
  return true
}
const getAppointment = (pid: string) => {
  const provider = providers.value.find((el) => el.pid === pid)
  if (!provider) return ''
  if (provider.role === 'NP' || provider.role == 'PA') return 'Medical'
  else if (provider.role === 'Massage Therapist') return 'Massage'
  else if (provider.role === 'Acupuncturist') return 'Acupuncture'
  return ''
}

const isNpPaProvider = (pid: string) => {
  const provider = providers.value.find((el) => el.pid === pid)
  return provider && (provider.role === 'NP' || provider.role === 'PA')
}

const onAddAppointmentClick = (split: { pid: string; id: number }) => {
  const provider = providers.value.find((el) => el.id === split.id)
  if (provider) {
    // Default to current time or 9:00 AM if before business hours
    const now = moment()
    const currentDate = moment(selectedDate.value)
    let startTime = '09:00'
    
    // If it's today and after 9 AM, use current time rounded to next 30-minute interval
    if (currentDate.isSame(now, 'day') && now.hour() >= 9) {
      const minutes = now.minute()
      const roundedMinutes = Math.ceil(minutes / 30) * 30
      const roundedTime = now.clone().minute(roundedMinutes).second(0)
      startTime = roundedTime.format('HH:mm')
    }
    
    providerField.value.start = `${currentDate.format('YYYY-MM-DD')} ${startTime}`
    providerField.value.duration = '30 minutes'
    providerField.value.pid = provider.pid
    providerField.value.event = 'Appointment'
    providerField.value.appointment = getAppointment(provider.pid) as AppointmentType
    appointment.value = true
  }
}
const onCellClicked = () => {
  const highlightTimeFrame = document.querySelector('.highlight-timeframe.active') as HTMLElement
  try {
    const id = parseInt(highlightTimeFrame.ariaValueText || '0')
    const time = highlightTimeFrame.innerText
    const provider = providers.value.find((el) => el.id === id)
    if (time && id > 0 && provider) {
      providerField.value.start = `${moment(selectedDate.value).format('YYYY-MM-DD')} ${moment(time, 'h:mm A').format('HH:mm')}`
      providerField.value.duration = '30 minutes'
      providerField.value.pid = provider.pid
      providerField.value.event = 'Appointment'
      providerField.value.appointment = getAppointment(provider.pid) as AppointmentType
      appointment.value = true
    }
  } catch {
    /** */
  }
}
const setHighlight = () => {
  const eventCols = document.querySelectorAll('.vuecal__cell-content')
  eventCols.forEach((col, index) => {
    const eventBox = col as HTMLElement
    eventBox.addEventListener('mousemove', (event) => {
      const highlightCell = eventBox.querySelector('.highlight-cell') as HTMLElement
      const highlightTimeFrame = eventBox.querySelector('.highlight-timeframe') as HTMLElement
      if (highlightCell) {
        const rect = eventBox.getBoundingClientRect()
        const y = event.clientY - rect.top
        if (scaledTimeHeight.value) {
          const eventElements = eventBox.querySelectorAll('.vuecal__event')
          const length = Math.floor(y / scaledTimeHeight.value)
          const top = length * scaledTimeHeight.value
          if (isEmptyInArea(eventElements, top)) {
            highlightCell.style.top = `${top}px`
            highlightCell.style.height = `${scaledTimeHeight.value}px`
            highlightCell.style.display = 'block'
            if (highlightTimeFrame) {
              highlightTimeFrame.style.display = 'block'
              highlightTimeFrame.style.lineHeight = `${scaledTimeHeight.value}px`
              highlightTimeFrame.style.top = `${top}px`
              highlightTimeFrame.style.right = '6px'
              highlightTimeFrame.classList.add('active')
              highlightTimeFrame.ariaValueText = `${index + 1}`
              const minutes = timeRange.value.min + 30 * length
              const mTime = moment()
              mTime.hour(0)
              mTime.minute(0)
              mTime.second(0)
              highlightTimeFrame.innerText = mTime.add(minutes, 'minutes').format('h:mm A')
            }
          } else {
            highlightCell.style.display = 'none'
            highlightTimeFrame.style.display = 'none'
          }
        }
      }
    })
    eventBox.addEventListener('mouseleave', () => {
      const highlightCells = document.querySelectorAll('.highlight-cell')
      const highlightTimeFrames = document.querySelectorAll('.highlight-timeframe')
      highlightCells.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        box.style.height = '0px'
      })
      highlightTimeFrames.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        cell.classList.remove('active')
        box.ariaValueText = ''
        box.innerText = ''
      })
    })
  })
}
watch(selectedDate, () => {
  setTimeout(() => {
    setHighlight()
  }, 1000)
})

// Re-initialize highlight when zoom changes
watch(zoomLevel, () => {
  setTimeout(() => {
    setHighlight()
  }, 100)
})

// Update CSS custom property for mobile scaling
watch(
  scaledMinWidth,
  (newWidth) => {
    document.documentElement.style.setProperty('--scaled-min-width', `${newWidth}px`)
  },
  { immediate: true },
)
</script>
<style lang="scss">
// Desktop toolbar styles
.desktop-toolbar {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eeeeee;
  background: white;
  border-top-left-radius: 20px;

  .toolbar-search {
    max-width: 300px;
  }
}

.mmm-calendar-header {
  height: 52px;
  border-top-left-radius: 20px;
  border-bottom: 1px solid #eeeeee;
  background: white;
}

// Mobile date header styles
.mobile-date-header {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #eeeeee;

  .mobile-header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .current-date-display {
    font-size: 15px;
    font-weight: 600;
    color: #1a1a1a;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .mobile-today-btn {
    padding: 4px 12px;
    height: 28px;
    font-size: 12px;
    margin-left: 8px;
  }
}

.mobile-date-switcher {
  .date-switcher-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .week-dates {
    display: flex;
    flex: 1;
    justify-content: space-around;
    gap: 4px;
  }

  .date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    position: relative;
    z-index: 10;

    .day-name {
      font-size: 11px;
      color: #666;
      margin-bottom: 2px;
      text-transform: uppercase;
      font-weight: 500;
    }

    .day-number {
      font-size: 14px;
      font-weight: 600;
      color: #1a1a1a;
    }

    &.today {
      .day-number {
        color: #1976d2;
      }
    }

    &.active {
      background: #e3f2fd;

      .day-name {
        color: #1976d2;
      }

      .day-number {
        color: #1976d2;
      }
    }

    &:hover:not(.active) {
      background: #f5f5f5;
    }
  }

  .nav-btn {
    min-width: 32px;
    min-height: 32px;
  }
}

// Floating zoom controls styles
.floating-zoom-controls {
  position: fixed;
  bottom: 80px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 8px 16px;
  z-index: 150;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .zoom-btn {
    min-width: 40px;
    min-height: 40px;

    &:disabled {
      opacity: 0.4;
    }

    &.reset-btn {
      min-width: 32px;
      min-height: 32px;
      margin-left: 4px;
    }
  }

  .zoom-level-display {
    font-size: 14px;
    font-weight: 600;
    color: white;
    min-width: 50px;
    text-align: center;
  }
}

// Mobile zoom controls styles
.mobile-zoom-controls {
  position: absolute;
  top: 7px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border-radius: 20px;
  padding: 4px 8px;
  z-index: 101;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .mobile-zoom-btn {
    min-width: 28px;
    min-height: 28px;

    &:disabled {
      opacity: 0.4;
    }
  }

  .mobile-zoom-level {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    min-width: 35px;
    text-align: center;
  }
}

// Calendar wrapper - mobile responsive
.calendar-wrapper {
  position: relative;
  background: white;

  @media (max-width: 1023px) {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .mobile-date-header {
      flex-shrink: 0;
    }

    .calendar-container {
      flex: 1;
      height: auto !important;
    }

    .mmm-calendar-header {
      display: none;
    }
  }
}

// Calendar container with mobile scrolling
.calendar-container {
  position: relative;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  height: calc(100vh - 200px);

  @media (max-width: 1023px) {
    height: calc(100vh - 100px);
    overflow-x: auto;
    overflow-y: auto;
    width: 100%;
    touch-action: pan-x pan-y;

    // Create a stacking context for proper sticky positioning on mobile
    transform: translateZ(0);
  }
}

// Inner wrapper to handle dynamic height
.calendar-inner-wrapper {
  position: relative;
  width: 100%;
  overflow: visible;

  // Ensure it can expand beyond container when zoomed
  min-height: 100%;
}

.scroll {
  overflow: hidden;
}

.vuecal {
  box-shadow: none;

  // Ensure proper height handling with zoom
  height: auto !important;
  min-height: 100%;

  // Override any vue-cal height restrictions
  .vuecal__body,
  .vuecal__cells,
  .vuecal__bg {
    height: auto !important;
  }

  // Mobile optimizations
  @media (max-width: 1023px) {
    min-width: max-content;
    width: max-content;
  }

  .vuecal__header {
    display: none;
    /*
    height: 48px;
    @media only screen and (max-width: 800px) {
      width: 100%;
    }
    .vuecal__split-days-headers {
      margin-left: 4em;
      height: 100%;
      .day-split-header {
        border-left: 1px solid #dddddd;
        .mmm-split-description {
          margin-top: -4px;
        }
      }
    }
    .header-title {
      font-size: 0.875rem;
      @media only screen and (max-width: 800px) {
        font-size: 0.5rem;
      }
    }
    .vuecal__menu {
      display: none;
    }
    .vuecal__title-bar {
      display: none;
    }
    */
  }
  .vuecal__body {
    // Ensure proper height handling with zoom
    height: auto !important;
    min-height: 100%;

    // Mobile optimizations
    @media (max-width: 1023px) {
      min-width: max-content;
      width: max-content;
      overflow-x: auto;
      overflow-y: visible;
      -webkit-overflow-scrolling: touch;
    }

    .vuecal__time-column {
      margin-top: 48px !important;
      position: sticky;
      left: 0;
      z-index: 1001; // Higher than split headers to stay on top
      background: white;
      border-right: 1px solid #eeeeee;

      @media (max-width: 1023px) {
        margin-top: 0 !important;
      }

      // Ensure the time column header area is also sticky
      &::before {
        content: '';
        position: absolute;
        top: -48px;
        left: 0;
        right: 0;
        height: 48px;
        background: white;
        border-bottom: 1px solid #eeeeee;
        z-index: 1001;

        // Hide the pseudo-element on mobile since margin-top is 0
        @media (max-width: 1023px) {
          display: none;
        }
      }
    }
    .vuecal__split-days-headers {
      height: 48px;
      position: sticky;
      top: 0;
      z-index: 1000;
      background: white;
      border-bottom: 1px solid #eeeeee;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .day-split-header {
        border-left: 1px solid #dddddd;
        background: white; // Ensure background is solid
        position: relative;

        // Add subtle hover effect
        &:hover {
          background: #f8f9fa;
        }

        .mmm-split-description {
          margin-top: -4px;
        }

      }
    }
    .vuecal__bg {
      overflow: hidden;
    }
    .vuecal__time-column {
      width: 4em;
    }
    .vuecal__cells.day-view {
      position: relative;
      height: auto !important; // Allow cells to expand with zoom

      @media (max-width: 1023px) {
        min-width: max-content;
        width: max-content;
      }

      .vuecal__cell {
        &::before {
          right: 0;
        }
        .vuecal__cell-content {
          margin-right: 1px;
          border-left: 1px solid rgba(0, 0, 0, 0.1);
          position: relative;

          // Mobile-specific adjustment to compensate for removed time column margin
          @media (max-width: 1023px) {
            transform: translateY(-48px);
          }

          .vuecal__event {
            padding-left: 1px;
            &:not(.vuecal__event--background) {
              background-color: transparent;
              z-index: 1;
              &:hover {
                z-index: 2;
              }
            }
          }
          .vuecal__event--focus {
            box-shadow: none;
          }

          // Add appointment button in cells
          .add-appointment-cell-btn {
            position: absolute;
            top: 50%;
            right: 4px;
            transform: translateY(-50%);
            z-index: 100;
            opacity: 0.6;
            transition: opacity 0.2s ease;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

            &:hover {
              opacity: 1;
              background: white;
            }
          }
        }
      }
    }
  }
}
.vuecal__no-event {
  display: none;
}
.vuecal__time-cell {
  cursor: pointer;
}
.highlight-timeframe {
  position: absolute;
  z-index: 1; /* Ensures it's behind the content */
  color: black;
}
.highlight-cell {
  position: absolute;
  background-color: #eaf6ff;
  z-index: 2; /* Ensures it's behind the content */
  opacity: 0.6;
  border: 2px dashed #6abdff;
  display: none;
  width: 100%;
  cursor: pointer;
}
.np-pa-flex-width {
  flex: 2 !important;
}
.vuecal__cell-split {
  flex-grow: 1;
  flex-basis: 0 !important;

  // Ensure consistent width on mobile - now responsive to zoom
  @media (max-width: 1023px) {
    width: var(--scaled-min-width, 180px) !important;
    min-width: var(--scaled-min-width, 180px) !important;
    max-width: var(--scaled-min-width, 180px) !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    flex-basis: var(--scaled-min-width, 180px) !important;
  }
}
.vuecal__event--background {
  z-index: -1;
}

@media (min-width: 320px) and (max-width: 480px) {
  .np-pa-flex-width {
    flex: 1 !important;
  }
}
</style>
