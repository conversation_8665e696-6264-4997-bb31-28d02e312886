<template>
  <div class="bg-white calendar-wrapper" style="border-top-left-radius: 20px">
    <!-- Desktop toolbar - only visible on desktop -->
    <div class="desktop-toolbar gt-sm">
      <q-btn
        outline
        class="q-px-xl"
        color="grey-9"
        label="Today"
        rounded
        @click="onToday"
        no-caps
      />
      <q-btn
        icon="arrow_back_ios"
        dense
        class="q-ml-xl"
        color="grey-9"
        flat
        round
        :ripple="false"
        @click="onPrev"
      />
      <q-btn
        class="q-ml-sm"
        icon="arrow_forward_ios"
        color="grey-9"
        flat
        round
        :ripple="false"
        dense
        @click="onNext"
      />
      <div class="text-grey-9 q-ml-md text-h6">{{ selectedDate }}</div>
      <q-space />
      <q-input
        dense
        filled
        v-model="patientName"
        placeholder="Search by patient"
        unelevated
        no-caps
        class="toolbar-search"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>
    </div>

    <!-- Mobile date header - only visible on mobile -->
    <div class="mobile-date-header lt-md">
      <div class="current-date-display">
        {{ currentDateFormatted }}
      </div>
      <div class="mobile-date-switcher">
        <div class="date-switcher-container">
          <q-btn
            icon="chevron_left"
            flat
            round
            size="sm"
            color="grey-7"
            @click="onPrevWeek"
            class="nav-btn"
          />
          <div class="week-dates">
            <div
              v-for="date in weekDates"
              :key="date.value"
              :class="['date-item', { active: date.isSelected, today: date.isToday }]"
              @click="onDateSelect(date.value)"
            >
              <div class="day-name">{{ date.dayName }}</div>
              <div class="day-number">{{ date.dayNumber }}</div>
            </div>
          </div>
          <q-btn
            icon="chevron_right"
            flat
            round
            size="sm"
            color="grey-7"
            @click="onNextWeek"
            class="nav-btn"
          />
        </div>
      </div>
    </div>

    <!-- Desktop header - hidden on mobile -->
    <div class="mmm-calendar-header row q-pl-lg items-center text-grey-9 gt-sm">
      <q-icon name="event_available" size="1.7em" />
      <div class="text-h6 q-ml-xs">{{ realEvents.length }}</div>
      <div class="text-body2 q-ml-xs">Total Appointments</div>
    </div>

    <!-- Custom sticky employee headers -->
    <div
      class="custom-employee-headers"
      ref="employeeHeadersRef"
      :style="{
        transform: `scale(${zoomLevel})`,
        transformOrigin: 'left top',
      }"
    >
      <div class="time-column-header"></div>
      <div
        v-for="(provider, index) in providers"
        :key="provider.id"
        class="custom-employee-header"
        :style="{
          width: `${minWidth}px`,
          minWidth: `${minWidth}px`,
          maxWidth: `${minWidth}px`,
          flexShrink: '0',
        }"
      >
        <div class="employee-info">
          <div class="gt-md text-body1">
            <strong>{{ provider.label }}</strong>
          </div>
          <div class="lt-lg text-body2">
            <strong>{{ provider.label }}</strong>
          </div>
          <div class="text-caption gt-md mmm-split-description">
            Appointments: {{ splitLen(provider.id) }}
          </div>
          <div class="text-caption lt-lg mmm-split-description text-center">
            ({{ splitLen(provider.id) }})
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar container with synchronized scrolling -->
    <div
      class="calendar-container"
      ref="calendarContainerRef"
      :style="{
        transform: `scale(${zoomLevel})`,
        transformOrigin: 'left bottom 80px',
      }"
    >
      <vue-cal
        :style="{
          minWidth: `${dynamicMinWidth}px`,
        }"
        :disable-views="['years', 'year', 'month']"
        active-view="day"
        :split-days="providers"
        :events="events"
        :sticky-split-labels="false"
        :time-from="timeRange.min"
        :time-step="30"
        :time-to="timeRange.max"
        :selected-date="calendarDate"
        time-format="hh:mm AM"
        :watch-real-time="true"
        :time-cell-height="timeRange.height"
        :min-cell-width="minWidth"
        :min-split-width="minWidth"
        :max-split-width="minWidth"
        class="responsive-calendar"
      >
        <template #event="{ event }">
          <event-component v-bind="event" />
        </template>
        <template #cell-content="{}">
          <div @click="onCellClicked" class="highlight-cell"></div>
          <div class="highlight-timeframe"></div>
        </template>
        <template #time-cell="{ hours, minutes }">
          <div
            :class="{ 'vuecal__time-cell-line': true, hours: !minutes }"
            style="font-size: 0.6rem"
          >
            {{ moment(`${hours}:${minutes}`, 'H:m').format('hh:mm A') }}
          </div>
        </template>
      </vue-cal>
    </div>
    <q-dialog v-model="appointment" persistent>
      <template #default>
        <appointment-modal v-bind="providerField" @close="appointment = false" />
      </template>
    </q-dialog>

    <!-- Floating zoom controls -->
    <div class="floating-zoom-controls">
      <q-btn
        icon="zoom_out"
        flat
        round
        size="md"
        color="white"
        @click="zoomOut"
        :disable="zoomLevel <= 0.5"
        class="zoom-btn"
      />
      <div class="zoom-level-display">{{ Math.round(zoomLevel * 100) }}%</div>
      <q-btn
        icon="zoom_in"
        flat
        round
        size="md"
        color="white"
        @click="zoomIn"
        :disable="zoomLevel >= 2"
        class="zoom-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, watch, ref } from 'vue'
import moment from 'moment'
import VueCal from 'vue-cal'
import EventComponent from 'components/event/EventComponent.vue'
import 'vue-cal/dist/vuecal.css'
import { useEvent } from 'src/composables/useEvent'
import { DEFAULT_PROVIDER_FIELD } from 'components/variable.js'
import AppointmentModal from 'components/event/AppointmentModal.vue'
import { US_TIME_FORMAT } from 'src/components/variable'
import { useFilterStore } from 'stores/filter'
import { useScheduleStore } from 'stores/schedule'
import type { AppointmentType, ProviderField } from '../models'

const minWidth = ref(240)
const zoomLevel = ref(1)
const appointment = ref(false)

// Refs for custom employee headers
const employeeHeadersRef = ref(null)
const calendarContainerRef = ref(null)

const dynamicMinWidth = computed(() => {
  // Calculate total width needed: time column (70px) + providers * minWidth, adjusted for zoom
  const timeColumnWidth = 70
  const adjustedMinWidth = minWidth.value * zoomLevel.value
  const totalWidth = timeColumnWidth + providers.value.length * adjustedMinWidth
  console.log('📐 DYNAMIC WIDTH CALC:', {
    providersCount: providers.value.length,
    minWidthPerProvider: adjustedMinWidth,
    zoomLevel: zoomLevel.value,
    timeColumnWidth,
    totalCalculatedWidth: totalWidth,
  })
  return totalWidth
})
const { selectedDate: calendarDate, timeRange, events, providers } = useEvent()
const filterStore = useFilterStore()
const scheduleStore = useScheduleStore()
const realEvents = computed(() =>
  events.value.filter(
    (el) =>
      !el.non_event &&
      el.event === 'Appointment' &&
      el.status !== 'Cancelled' &&
      el.status !== 'No Show',
  ),
)
const providerField = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)
const splitLen = computed(
  () => (id: number) => realEvents.value.filter((el) => el.split === id).length,
)

// Toolbar functionality
const selectedDate = computed(() =>
  moment(filterStore._date, US_TIME_FORMAT).format('dddd, MMMM D, YYYY'),
)
const patientName = computed({
  get: () => filterStore._patientName,
  set: (val) => filterStore.changePatient(val),
})

const onToday = () => {
  const date = moment().format(US_TIME_FORMAT)
  filterStore.changeDate(date)
}

const onPrev = async () => {
  const prevMoment = moment(filterStore._date, US_TIME_FORMAT).subtract(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(prevMoment)
  filterStore.changeDate(prevMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onNext = async () => {
  const nextMoment = moment(filterStore._date, US_TIME_FORMAT).add(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(nextMoment)
  filterStore.changeDate(nextMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

// Mobile date functionality
const currentDateFormatted = computed(() => {
  return moment(calendarDate.value).format('dddd, MMMM D, YYYY')
})

const weekDates = computed(() => {
  const startOfWeek = moment(calendarDate.value).startOf('week')
  const dates = []

  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.clone().add(i, 'days')
    dates.push({
      value: date.format(US_TIME_FORMAT),
      dayName: date.format('ddd'),
      dayNumber: date.format('D'),
      isSelected: date.isSame(calendarDate.value, 'day'),
      isToday: date.isSame(moment(), 'day'),
    })
  }

  return dates
})

// Mobile navigation methods
const onDateSelect = async (dateValue: string) => {
  const hasSchedule = scheduleStore.hasSchedule(moment(dateValue, US_TIME_FORMAT))
  filterStore.changeDate(dateValue)
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onPrevWeek = async () => {
  const prevWeek = moment(calendarDate.value).subtract(1, 'week')
  const hasSchedule = scheduleStore.hasSchedule(prevWeek)
  filterStore.changeDate(prevWeek.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

const onNextWeek = async () => {
  const nextWeek = moment(calendarDate.value).add(1, 'week')
  const hasSchedule = scheduleStore.hasSchedule(nextWeek)
  filterStore.changeDate(nextWeek.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}

// Zoom functions
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

// Simple horizontal scroll synchronization
const setupScrollSync = () => {
  if (!calendarContainerRef.value || !employeeHeadersRef.value) return

  const calendarContainer = calendarContainerRef.value
  const employeeHeaders = employeeHeadersRef.value

  calendarContainer.addEventListener('scroll', () => {
    employeeHeaders.scrollLeft = calendarContainer.scrollLeft
  })

  console.log('📜 HORIZONTAL SCROLL SYNC INITIALIZED')
}

// Diagnostic function to log calendar dimensions and scroll behavior
const logCalendarDimensions = () => {
  setTimeout(() => {
    const calendarContainer = document.querySelector('.calendar-container') as HTMLElement
    const vuecalBody = document.querySelector('.vuecal__body') as HTMLElement
    const responsiveCalendar = document.querySelector('.responsive-calendar') as HTMLElement

    if (calendarContainer && vuecalBody && responsiveCalendar) {
      console.log('📱 MOBILE CALENDAR DIAGNOSTICS:')
      console.log('Container dimensions:', {
        width: calendarContainer.offsetWidth,
        height: calendarContainer.offsetHeight,
        scrollWidth: calendarContainer.scrollWidth,
        scrollHeight: calendarContainer.scrollHeight,
        scrollLeft: calendarContainer.scrollLeft,
        scrollTop: calendarContainer.scrollTop,
      })
      console.log('VueCal body dimensions:', {
        width: vuecalBody.offsetWidth,
        height: vuecalBody.offsetHeight,
        scrollWidth: vuecalBody.scrollWidth,
      })
      console.log('Responsive calendar:', {
        width: responsiveCalendar.offsetWidth,
        transform: responsiveCalendar.style.transform,
        transformOrigin: responsiveCalendar.style.transformOrigin,
      })

      // Check sticky elements
      const stickyElements = document.querySelectorAll(
        '[style*="sticky"], .mobile-date-header, .vuecal__split-days-headers, .vuecal__time-column',
      )
      console.log('Sticky elements count:', stickyElements.length)
      stickyElements.forEach((el, index) => {
        const element = el as HTMLElement
        console.log(`Sticky element ${index}:`, {
          className: element.className,
          position: getComputedStyle(element).position,
          zIndex: getComputedStyle(element).zIndex,
          top: getComputedStyle(element).top,
        })
      })
    }
  }, 100)
}

// Mobile viewport diagnostics
const logMobileViewport = () => {
  console.log('📱 MOBILE VIEWPORT DIAGNOSTICS:')
  console.log('Screen dimensions:', {
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
  })
  console.log('Window dimensions:', {
    innerWidth: window.innerWidth,
    innerHeight: window.innerHeight,
    outerWidth: window.outerWidth,
    outerHeight: window.outerHeight,
  })
  console.log('Document dimensions:', {
    documentWidth: document.documentElement.clientWidth,
    documentHeight: document.documentElement.clientHeight,
    bodyWidth: document.body.clientWidth,
    bodyHeight: document.body.clientHeight,
  })
  console.log('Device info:', {
    userAgent: navigator.userAgent,
    devicePixelRatio: window.devicePixelRatio,
    orientation: window.screen.orientation?.type || 'unknown',
  })
}

const isEmptyInArea = (elements: NodeListOf<Element>, hoverTop: number) => {
  for (let i = 0; i < elements.length; i++) {
    const element = elements[i] as HTMLElement
    try {
      const hoverHeight = timeRange.value.height || 0
      const elementTop = parseInt(element.style.top)
      const elementHeight = parseInt(element.style.height)
      const bottom = Math.min(elementTop + elementHeight, hoverTop + hoverHeight)
      const top = Math.max(elementTop, hoverTop)
      if (bottom > top) return false
    } catch {
      /** */
    }
  }
  return true
}

const getAppointment = (pid: string) => {
  const provider = providers.value.find((el) => el.pid === pid)
  if (!provider) return ''
  if (provider.role === 'NP' || provider.role == 'PA') return 'Medical'
  else if (provider.role === 'Massage Therapist') return 'Massage'
  else if (provider.role === 'Acupuncturist') return 'Acupuncture'
  return ''
}

const onCellClicked = () => {
  const highlightTimeFrame = document.querySelector('.highlight-timeframe.active') as HTMLElement
  try {
    const id = parseInt(highlightTimeFrame.ariaValueText || '0')
    const time = highlightTimeFrame.innerText
    const provider = providers.value.find((el) => el.id === id)
    if (time && id > 0 && provider) {
      providerField.value.start = `${moment(calendarDate.value).format('YYYY-MM-DD')} ${moment(time, 'h:mm A').format('HH:mm')}`
      providerField.value.duration = '30 minutes'
      providerField.value.pid = provider.pid
      providerField.value.event = 'Appointment'
      providerField.value.appointment = getAppointment(provider.pid) as AppointmentType
      appointment.value = true
    }
  } catch {
    /** */
  }
}

const setHighlight = () => {
  const eventCols = document.querySelectorAll('.vuecal__cell-content')
  eventCols.forEach((col, index) => {
    const eventBox = col as HTMLElement
    eventBox.addEventListener('mousemove', (event) => {
      const highlightCell = eventBox.querySelector('.highlight-cell') as HTMLElement
      const highlightTimeFrame = eventBox.querySelector('.highlight-timeframe') as HTMLElement
      if (highlightCell) {
        const rect = eventBox.getBoundingClientRect()
        const y = event.clientY - rect.top
        if (timeRange.value.height) {
          const eventElements = eventBox.querySelectorAll('.vuecal__event')
          const length = Math.floor(y / timeRange.value.height)
          const top = length * timeRange.value.height
          if (isEmptyInArea(eventElements, top)) {
            highlightCell.style.top = `${top}px`
            highlightCell.style.height = `${timeRange.value.height}px`
            highlightCell.style.display = 'block'
            if (highlightTimeFrame) {
              highlightTimeFrame.style.display = 'block'
              highlightTimeFrame.style.lineHeight = `${timeRange.value.height}px`
              highlightTimeFrame.style.top = `${top}px`
              highlightTimeFrame.style.right = '6px'
              highlightTimeFrame.classList.add('active')
              highlightTimeFrame.ariaValueText = `${index + 1}`
              const minutes = timeRange.value.min + 30 * length
              const mTime = moment()
              mTime.hour(0)
              mTime.minute(0)
              mTime.second(0)
              highlightTimeFrame.innerText = mTime.add(minutes, 'minutes').format('h:mm A')
            }
          } else {
            highlightCell.style.display = 'none'
            highlightTimeFrame.style.display = 'none'
          }
        }
      }
    })
    eventBox.addEventListener('mouseleave', () => {
      const highlightCells = document.querySelectorAll('.highlight-cell')
      const highlightTimeFrames = document.querySelectorAll('.highlight-timeframe')
      highlightCells.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        box.style.height = '0px'
      })
      highlightTimeFrames.forEach((cell) => {
        const box = cell as HTMLElement
        box.style.display = 'none'
        cell.classList.remove('active')
        box.ariaValueText = ''
        box.innerText = ''
      })
    })
  })
}

onMounted(() => {
  setHighlight()

  // Setup simple scroll synchronization
  setTimeout(() => {
    setupScrollSync()

    // Calculate mobile header height and set CSS variable
    const mobileHeader = document.querySelector('.mobile-date-header') as HTMLElement
    if (mobileHeader) {
      const headerHeight = mobileHeader.offsetHeight
      document.documentElement.style.setProperty('--mobile-header-height', `${headerHeight}px`)
    }
  }, 500)
})

watch(calendarDate, () => {
  setTimeout(() => {
    setHighlight()
  }, 1000)
})
</script>

<style lang="scss">
.mmm-calendar-header {
  height: 52px;
  border-top-left-radius: 20px;
  border-bottom: 1px solid #eeeeee;
  background: white;
}

// Desktop toolbar styles
.desktop-toolbar {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 24px;
  background: white;
  border-bottom: 1px solid #eeeeee;

  .toolbar-search {
    border-radius: 10px;
    min-width: 200px;
    background-color: #f5f5f5;

    .q-field__control {
      border-radius: 10px;
      &::after {
        background: transparent;
      }
    }
  }
}

// Mobile date header styles
.mobile-date-header {
  position: sticky;
  top: 0;
  z-index: 1001;
  padding: 12px 16px; // Reduced vertical padding
  background: white;
  border-bottom: 1px solid #eeeeee;

  .current-date-display {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px; // Reduced margin
    text-align: center;
  }
}

.mobile-date-switcher {
  .date-switcher-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  .week-dates {
    display: flex;
    flex: 1;
    justify-content: space-around;
    gap: 4px;
  }

  .date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;

    .day-name {
      font-size: 11px;
      color: #666;
      margin-bottom: 2px;
      text-transform: uppercase;
      font-weight: 500;
    }

    .day-number {
      font-size: 14px;
      font-weight: 600;
      color: #1a1a1a;
    }

    &.today {
      .day-number {
        color: #1976d2;
      }
    }

    &.active {
      background: #e3f2fd;

      .day-name {
        color: #1976d2;
      }

      .day-number {
        color: #1976d2;
      }
    }

    &:hover:not(.active) {
      background: #f5f5f5;
    }
  }

  .nav-btn {
    min-width: 32px;
    min-height: 32px;
  }

  &.compact {
    .date-item {
      padding: 4px 2px;
      min-width: 28px;

      .day-name {
        font-size: 10px;
      }

      .day-number {
        font-size: 12px;
      }
    }

    .nav-btn {
      min-width: 28px;
      min-height: 28px;
    }
  }
}

// Floating zoom controls styles
.floating-zoom-controls {
  position: fixed;
  bottom: 60px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 8px 16px;
  z-index: 1500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .zoom-btn {
    min-width: 40px;
    min-height: 40px;

    &:disabled {
      opacity: 0.4;
    }
  }

  .zoom-level-display {
    font-size: 14px;
    font-weight: 600;
    color: white;
    min-width: 50px;
    text-align: center;
  }
}

// Calendar wrapper - clean and simple
.calendar-wrapper {
  position: relative;
  background: white;

  // Debug: ensure no unexpected spacing
  @media (max-width: 1023px) {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .mobile-date-header {
      flex-shrink: 0;
    }

    .calendar-container {
      flex: 1;
      height: auto !important; // Override height calculation
    }

    .mmm-calendar-header {
      display: none; // Hide desktop header on mobile
    }
  }
}

// Custom sticky employee headers
.custom-employee-headers {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  height: 48px;
  overflow: hidden;

  @media (max-width: 1023px) {
    top: var(--mobile-header-height, 84px);
    overflow-x: auto;
    overflow-y: hidden;
    // Hide scrollbar
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .time-column-header {
    background: white;
    border-right: 1px solid #eeeeee;
    flex-shrink: 0;
    width: 70px;
  }

  .custom-employee-header {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 1px;
      background: rgba(0, 0, 0, 0.1);
    }

    .employee-info {
      text-align: center;
      width: 100%;
      padding: 8px 4px;

      .mmm-split-description {
        margin-top: -4px;
      }
    }
  }
}

// Calendar container with proper scrolling
.calendar-container {
  position: relative;
  height: calc(100vh - 200px);
  overflow: auto;
  -webkit-overflow-scrolling: touch;

  @media (max-width: 1023px) {
    height: calc(100vh - var(--mobile-header-height, 100px));
    overflow-x: auto;
    overflow-y: auto;
    width: 100%;
    // Ensure horizontal scrolling works
    touch-action: pan-x pan-y;
    // Force scroll container behavior
    contain: layout style;
  }
}

// Responsive calendar styles
.responsive-calendar {
  width: 100%;

  .vuecal__header {
    display: none;
  }

  // Force minimum widths on mobile to enable horizontal scrolling
  @media (max-width: 1023px) {
    min-width: max-content;
    width: max-content;

    .vuecal__body {
      min-width: max-content;
      width: max-content;
    }
  }

  .vuecal__body {
    .vuecal__split-days-headers {
      display: none !important; // Completely hide vue-cal's built-in headers
    }

    .vuecal__time-column {
      width: 70px;
      position: sticky;
      left: 0;
      z-index: 999;
      background: white;
      border-right: 1px solid #eeeeee;
    }

    // Mobile optimizations - ensure horizontal scrolling works
    @media (max-width: 1023px) {
      width: 100%;
      overflow-x: auto;
      overflow-y: visible;
      -webkit-overflow-scrolling: touch;

      .vuecal__cells.day-view {
        min-width: max-content;
        width: max-content;
      }

      .vuecal__split-days-headers {
        min-width: max-content;
        width: max-content;
      }
    }
  }

  .vuecal__cells.day-view {
    position: relative;

    .vuecal__cell {
      &::before {
        right: 0;
      }

      .vuecal__cell-content {
        margin-right: 1px;
        border-left: 1px solid rgba(0, 0, 0, 0.1);
        position: relative;

        .vuecal__event {
          padding-left: 1px;
          &:not(.vuecal__event--background) {
            background-color: transparent;
            z-index: 1;
            &:hover {
              z-index: 2;
            }
          }
        }

        .vuecal__event--focus {
          box-shadow: none;
        }
      }
    }
  }
}

.vuecal__no-event {
  display: none;
}
.vuecal__time-cell {
  cursor: pointer;
}
.highlight-timeframe {
  position: absolute;
  z-index: 1; /* Ensures it's behind the content */
  color: black;
}
.highlight-cell {
  position: absolute;
  background-color: #eaf6ff;
  z-index: 2; /* Ensures it's behind the content */
  opacity: 0.6;
  border: 2px dashed #6abdff;
  display: none;
  width: 100%;
  cursor: pointer;
}
.np-pa-flex-width {
  flex: 2 !important;
}
.vuecal__cell-split {
  width: 240px !important;
  min-width: 240px !important;
  max-width: 240px !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  flex-basis: 240px !important;

  // Ensure consistent width on all screen sizes
  @media (max-width: 1023px) {
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
    flex-shrink: 0 !important;
  }
}
.vuecal__event--background {
  z-index: -1;
}
</style>
