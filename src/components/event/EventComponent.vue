<template>
  <div 
    class="full-height event-area row items-center" 
    :class="{ 
      'cancelled-narrow': status === 'Cancelled' || status === 'CancelledGroup',
      'cancelled-group': status === 'CancelledGroup'
    }"
    :style="areaStyle"
  >
    <!-- Show cancelled group with multiple red bars -->
    <template v-if="status === 'CancelledGroup'">
      <div
        v-for="(cancelledEvent, index) in cancelledAppointments"
        :key="`cancelled-group-${index}`"
        class="cancelled-box"
        @click.stop="onClickCancelled(cancelledEvent)"
        :title="cancelledEvent.name"
      ></div>
    </template>
    <!-- Show single cancelled box for individual cancelled appointments -->
    <div
      v-else-if="status === 'Cancelled'"
      class="cancelled-box"
      @click.stop="onClick"
    ></div>
    <!-- Show individual cancelled events that are attached to active appointments -->
    <div
      v-for="(cancelledEvent, index) in cancelledEvents"
      :key="`cancelled-${index}`"
      class="cancelled-box"
      @click.stop="onClickCancelled(cancelledEvent)"
    ></div>
    <div
      v-if="!non_event && status !== 'Cancelled' && status !== 'CancelledGroup'"
      class="full-height q-pa-none event-box col"
      :style="boxStyle"
      :data-event-id="id"
      :data-start-time="start"
      :data-provider="pid"
      @click="onClick"
    >
      <template v-if="event === 'Appointment'">
        <template v-if="status !== 'Cancelled'">
          <div class="full-width full-height row q-pr-xs appointment-content">
            <div class="col q-pr-xs appointment-text">
              <div class="patient-name text-body2 mmm-ellipsis row items-center">
                <span>{{ label }}</span>
                <q-icon
                  v-if="hasBalanceAlert"
                  name="warning"
                  size="xs"
                  color="orange"
                  class="q-ml-xs balance-alert-icon"
                >
                  <q-tooltip class="bg-orange text-white">
                    Patient has outstanding balance
                  </q-tooltip>
                </q-icon>
              </div>
              <div class="time-slot mmm-ellipsis">
                {{ timeSlot }}
              </div>
            </div>
            <event-mark
              :is-new-patient="isNewPatient"
              :notes="notes"
              :status="status"
              :group="group"
              :duration="duration"
              :is-birthday-week="isBirthdayWeek"
            />
          </div>
        </template>
        <template v-else>
          <div class="full-width full-height row q-pr-xs cancelled-appointment-content">
            <div class="col q-pr-xs appointment-text">
              <div class="patient-name text-body2 mmm-ellipsis">
                {{ label }}
              </div>
              <div class="time-slot mmm-ellipsis">
                {{ timeSlot }}
              </div>
            </div>
          </div>
        </template>
      </template>
      <div v-else class="full-height row items-center justify-center">
        <div
          class="text-caption text-center bg-white rounded-borders q-px-sm text-grey-6"
          style="border: 1px solid #dddddd"
        >
          {{ label }}
        </div>
      </div>
    </div>
    <div 
      v-else-if="status === 'Cancelled' || status === 'CancelledGroup'"
      class="full-width full-height cancelled-clickable-area col"
      @click="onAddAppointmentClick"
      title="Click to add appointment"
    ></div>
    <div v-else class="full-width full-height"></div>
    <!-- Add + icon for appointments (cancelled takes priority over NP/PA) -->
    <q-btn
      v-if="shouldShowAnyAddButton"
      icon="add"
      round
      flat
      size="xs"
      color="primary"
      class="add-appointment-btn-inline"
      @click.stop="onAddAppointmentClick"
      title="Add appointment to same timeslot"
    />
  </div>
  
  <draggable-modal 
    v-model="appointmentOpen" 
    title="Appointment Details"
    :initial-width="500"
    :initial-height="730"
    @close="onCloseEventModal"
  >
    <appointment-modal v-bind="appointmentValue" @close="onCloseEventModal" />
  </draggable-modal>
  
  <draggable-modal 
    v-model="deleteOpen" 
    title="Delete Appointment"
    :initial-width="400"
    :initial-height="200"
    :resizable="false"
    @close="onCloseDeleteModal"
  >
    <delete-modal 
      v-if="appointmentValue && appointmentValue.id"
      :id="appointmentValue.id" 
      :type="appointmentValue.event"
      @close="onCloseDeleteModal" 
    />
  </draggable-modal>
</template>

<script setup lang="ts">
import moment from 'moment'
import type { ProviderField, StatusType } from '../models'
import { computed, ref, onMounted, nextTick, watch, onUnmounted } from 'vue'
import AppointmentModal from './AppointmentModal.vue'
import DeleteModal from './DeleteModal.vue'
import EventMark from './EventMark.vue'
import DraggableModal from '../common/DraggableModal.vue'
import { useClientStore } from 'stores/client'
import { useCenterStore } from 'stores/center'
import { storeToRefs } from 'pinia'
import { DEFAULT_PROVIDER_FIELD } from '../variable'
import { useAddButtonCoordination } from 'src/composables/useAddButtonCoordination'
const props = withDefaults(defineProps<ProviderField & {
  _eid?: string
  startTimeMinutes?: number
  endTimeMinutes?: number
  title?: string
  content?: string
  allDay?: boolean
  segments?: any
  repeat?: any
  daysCount?: number
  deletable?: boolean
  deleting?: boolean
  titleEditable?: boolean
  resizable?: boolean
  resizing?: boolean
  draggable?: boolean
  dragging?: boolean
  draggingStatic?: boolean
  focused?: boolean
}>(), {
  name: 'Time Off',
  event: 'Time Off',
  start: '',
  end: '',
  duration: '',
  notes: '',
  appointment: 'Medical',
  patient: () => [],
  status: '' as StatusType,
  split: 1,
  non_event: false,
  pid: '',
  isNewPatient: false,
  isBirthdayWeek: false,
  group: '',
  cancelled: () => [],
})
const appointmentOpen = ref(false)
const deleteOpen = ref(false)
const startTime = ref('')
const endTime = ref('')

const clientStore = useClientStore()
const centerStore = useCenterStore()
const { _clients } = storeToRefs(clientStore)
const { providers } = storeToRefs(centerStore)
const { 
  getTimeSlotId, 
  hasOtherButtonInTimeSlot, 
  ownsButtonForTimeSlot,
  registerAddButton, 
  unregisterAddButton 
} = useAddButtonCoordination()
const cancelledEvents = computed(() => props.cancelled)
const cancelledAppointments = computed(() => props.cancelledAppointments || [])
const appointmentValue = ref<ProviderField>(DEFAULT_PROVIDER_FIELD)
const boxStyle = computed(() => {
  if (props.event === 'Time Off') {
    return `background: repeating-linear-gradient(45deg, transparent, transparent 10px, #f2f2f2 10px, #f2f2f2 20px);`
  }
  let padding = 'padding: 6px 4px 4px 6px;'
  if (props.duration === '30 minutes') padding = 'padding: 6px 4px 4px 8px;'
  else if (props.duration === '60 minutes') padding = 'padding: 8px 6px 6px 10px;'

  let color = `background-color: #E4F3FF; border-left: 3px solid #6ABDFF; ${padding}` //pending
  if (props.status === 'Confirmed')
    color = `background-color: #F1F7EC; border-left: 3px solid #4CAF50; ${padding}`
  else if (props.status === 'Checked-in')
    color = `background-color: #FFF3E4; border-left: 3px solid #FF9800; ${padding}`
  else if (props.status === 'Complete')
    color = `background-color: #F5F5F5; border-left: 3px solid #9E9E9E; ${padding}`
  else if (props.status === 'No Show')
    color = `background-color: #FFEBEE; border-left: 3px solid #F44336; ${padding}`

  return color
})
const areaStyle = computed(() => {
  if (!props.non_event)
    return `
      border-left: 1px solid white;
      border-right: 1px solid white;
      border-bottom: 0px solid white;
      border-top: 0px solid white;
      margin: 0;
      padding: 0;
    `
  return ''
})

const isNewPatient = computed(() => props.isNewPatient)
const isBirthdayWeek = computed(() => props.isBirthdayWeek)
const group = computed(() => props.group)
const label = computed(() => removeEmojis(props.name))
const timeSlot = computed(() => {
  const start = moment(props.start).format('hh:mm A')
  const end = moment(props.end).format('hh:mm A')
  return `${start} - ${end}`
})

const hasBalanceAlert = computed(() => {
  if (props.event !== 'Appointment' || !props.patientId?.[0]) return false
  
  const patientId = props.patientId[0]
  const client = _clients.value.find(c => c.id === patientId)
  return client?.fields.Balance && client.fields.Balance > 0
})

const isNpPaProvider = computed(() => {
  const provider = providers.value.find((el) => el.id === props.pid)
  return provider && (provider.role === 'NP' || provider.role === 'PA')
})

const shouldShowAddButton = computed(() => {
  // Only show on NP/PA providers and appointments
  if (!isNpPaProvider.value || props.non_event || props.event !== 'Appointment') {
    return false
  }
  
  // Only show on the last event when there are multiple events
  return isLastEvent.value
})

const shouldShowCancelledAddButton = computed(() => {
  // Show + button for cancelled groups or individual last cancelled appointments
  return props.status === 'CancelledGroup' || 
         (props.status === 'Cancelled' && props.isLastCancelled === true)
})

const shouldShowPriorityButton = computed(() => {
  // Only show + button on NP/PA providers that meet the criteria
  if (!isNpPaProvider.value || props.non_event || props.event !== 'Appointment') return false
  
  const provider = providers.value.find((el) => el.id === props.pid)
  if (!provider) return false
  
  // Simplified approach: show + button on all NP/PA providers for now
  // The key is ensuring cancelled appointments take priority when they exist
  return true
})

const timeSlotId = computed(() => {
  return getTimeSlotId(props.start)
})

const shouldShowBasedOnPriority = computed(() => {
  // Priority order: Cancelled buttons take highest priority, then NP/PA buttons
  const hasCancelledButton = shouldShowCancelledAddButton.value
  const hasNpPaButton = shouldShowAddButton.value
  
  // Show cancelled buttons first (highest priority)
  if (hasCancelledButton) {
    return true
  }
  
  // Always show NP/PA buttons (no priority restrictions)
  if (hasNpPaButton) {
    return true
  }
  
  return false
})

const shouldShowAnyAddButton = computed(() => {
  // Phase 1: Check if this component is eligible to show a button
  if (!shouldShowBasedOnPriority.value) {
    return false
  }
  
  // Phase 2: Show button if this component owns it or no other component has it
  return ownsButtonForTimeSlot(timeSlotId.value) || !hasOtherButtonInTimeSlot(timeSlotId.value)
})

const isLastEvent = ref(true)

onMounted(() => {
  startTime.value = moment(props.start).format('HH:mm')
  endTime.value = moment(props.end).format('HH:mm')
  
  // Check if this is the last event in the time slot
  nextTick(() => {
    checkIfLastEvent()
    // Register for coordination if eligible
    tryRegisterButton()
  })
})

onUnmounted(() => {
  // Clean up button registration when component is destroyed
  unregisterAddButton(timeSlotId.value)
})

const tryRegisterButton = () => {
  // Only register if we're eligible and no other component has registered yet
  if (shouldShowBasedOnPriority.value && !hasOtherButtonInTimeSlot(timeSlotId.value)) {
    registerAddButton(timeSlotId.value)
  }
}

// Watch for changes that might affect event positioning
watch([() => props.id, () => props.start, () => props.pid], () => {
  nextTick(() => {
    checkIfLastEvent()
    // Re-evaluate coordination
    unregisterAddButton(timeSlotId.value)
    tryRegisterButton()
  })
}, { immediate: true })

// Watch for priority changes
watch(shouldShowBasedOnPriority, (newValue) => {
  nextTick(() => {
    if (newValue) {
      tryRegisterButton()
    } else {
      unregisterAddButton(timeSlotId.value)
    }
  })
})

const checkIfLastEvent = () => {
  // Simplified approach - just show the button on all NP/PA appointments
  // The complex DOM checking was causing issues on non-today dates
  isLastEvent.value = true
}


const removeEmojis = (str: string) => {
  // eslint-disable-next-line no-misleading-character-class
  return str.replace(/[\p{Emoji}\u200B-\u200D\uFE0F]/gu, '')
}
const onClick = () => {
  appointmentValue.value = props
  appointmentOpen.value = !appointmentOpen.value
}
const onClickCancelled = (event: ProviderField) => {
  appointmentValue.value = { ...event }
  appointmentOpen.value = !appointmentOpen.value
}
const onCloseEventModal = (isDelete?: boolean) => {
  console.log('Appointment modal closing, isDelete:', isDelete)
  appointmentOpen.value = false
  if (isDelete) {
    console.log('Opening delete modal in 100ms')
    // Add a small delay to ensure the appointment modal is fully closed
    // before opening the delete modal
    setTimeout(() => {
      console.log('Setting deleteOpen to true, appointmentValue:', appointmentValue.value)
      deleteOpen.value = true
    }, 100)
  }
}

const onCloseDeleteModal = () => {
  console.log('Delete modal closing')
  deleteOpen.value = false
}

const onAddAppointmentClick = () => {
  // Emit an event or call a parent method to handle adding appointment
  // For now, we'll use a simple approach - open appointment modal with same time
  appointmentValue.value = {
    ...DEFAULT_PROVIDER_FIELD,
    start: props.start,
    end: props.end,
    duration: props.duration,
    pid: props.pid,
    event: 'Appointment',
    appointment: getAppointmentType(),
  }
  appointmentOpen.value = true
}

const getAppointmentType = () => {
  const provider = providers.value.find((el) => el.id === props.pid)
  if (!provider) return 'Medical'
  if (provider.role === 'NP' || provider.role === 'PA') return 'Medical'
  else if (provider.role === 'Massage Therapist') return 'Massage'
  else if (provider.role === 'Acupuncturist') return 'Acupuncture'
  return 'Medical'
}
</script>
<style lang="scss">
.event-area {
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
  
  // Make individual cancelled appointments narrow to show just the red bar
  &.cancelled-narrow {
    // For single cancelled appointments, keep narrow
    &:not(.cancelled-group) {
      width: 16px !important;
      min-width: 16px !important;
      max-width: 16px !important;
      flex-shrink: 0;
    }
    
    // For cancelled groups, allow flexible width to accommodate multiple bars
    &.cancelled-group {
      min-width: fit-content;
      flex-shrink: 0;
    }
  }

  .cancelled-box {
    cursor: pointer;
    background-color: #ff4444;
    border-radius: 3px;
    width: 8px;
    margin-right: 4px;
    height: 100%;
    flex-shrink: 0;
    transition: all 0.2s ease;
    &:hover {
      background-color: #ff6666;
      width: 10px;
      box-shadow:
        0 1px 5px #0003,
        0 2px 2px #00000024,
        0 3px 1px -2px #0000001f;
    }
  }
  
  // When event area is cancelled-narrow, make the red bar take full width
  &.cancelled-narrow:not(.cancelled-group) .cancelled-box {
    width: 100%;
    margin-right: 0;
    border-radius: 3px;
  }
  
  // For cancelled groups, show multiple red bars with spacing
  &.cancelled-group .cancelled-box {
    width: 12px;
    margin-right: 4px;
    border-radius: 3px;
    
    &:last-child {
      margin-right: 8px; // Extra space before + button
    }
  }
  
  // Position + button for individual cancelled appointments
  &.cancelled-narrow:not(.cancelled-group) .add-appointment-btn-inline {
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10002;
  }
  
  // For cancelled groups, + button flows naturally after the red bars
  &.cancelled-group .add-appointment-btn-inline {
    position: relative;
    margin-left: 4px;
  }
  .event-box {
    min-height: auto;
    z-index: 10001;
    cursor: pointer;
    border-radius: 5px;
    text-align: left;
    margin: 0 !important;
    padding: 0 !important;
    height: 100% !important;
    &:hover {
      box-shadow:
        0 1px 5px #0003,
        0 2px 2px #00000024,
        0 3px 1px -2px #0000001f;
    }
  }

  .appointment-content {
    height: 100%;
    padding: 4px 6px;

    .appointment-text {
      .patient-name {
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 2px;
        
        .balance-alert-icon {
          flex-shrink: 0;
          margin-left: 4px;
        }
      }

      .time-slot {
        font-size: 0.65rem;
        color: #666;
        line-height: 1.1;
      }
    }
  }

  .cancelled-appointment-content {
    height: 100%;
    padding: 4px 6px;
    background-color: #ffebee;
    border-left: 3px solid #f44336;

    .appointment-text {
      .patient-name {
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 2px;
        color: #666;
      }

      .time-slot {
        font-size: 0.65rem;
        color: #999;
        line-height: 1.1;
      }
    }
  }

  .cancelled-clickable-area {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 5px;
    min-height: 100%;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

.add-appointment-btn-inline {
  flex-shrink: 0;
  margin-left: 4px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  order: 1;
  border: 1px solid;
  border-radius: 5px;

  &:hover {
    opacity: 1;
  }
}
</style>
