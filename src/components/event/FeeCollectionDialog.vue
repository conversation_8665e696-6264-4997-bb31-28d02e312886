<template>
  <q-card class="fee-dialog">
    <q-card-section>
      <div class="text-h6">Cancellation Fee</div>
      <div class="text-body2 text-grey-7 q-mt-sm">
        This appointment was marked as "{{ status }}" within 48 hours. 
        Would you like to add a cancellation fee to the patient's balance?
      </div>
    </q-card-section>

    <q-card-section class="q-pt-none">
      <div class="q-mb-md">
        <strong>Patient:</strong> {{ patientName }}
      </div>
      
      <q-input
        v-model.number="feeAmount"
        type="number"
        label="Fee Amount"
        prefix="$"
        outlined
        dense
        :rules="[val => val >= 0 || 'Amount must be positive']"
        class="q-mb-md"
      />
      
      <q-input
        v-model="feeNote"
        type="textarea"
        label="Note (optional)"
        outlined
        dense
        rows="2"
        placeholder="e.g., 48-hour cancellation fee"
      />
    </q-card-section>

    <q-card-actions align="right">
      <q-btn
        flat
        label="Skip"
        color="grey-7"
        @click="$emit('close', { action: 'skip' })"
        no-caps
      />
      <q-btn
        flat
        label="Cancel"
        color="grey-7"
        @click="$emit('close', { action: 'cancel' })"
        no-caps
      />
      <q-btn
        unelevated
        label="Add Fee"
        color="primary"
        @click="onAddFee"
        :disable="feeAmount <= 0"
        no-caps
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  patientName: string
  status: 'No Show' | 'Cancelled'
}

interface FeeResult {
  action: 'add-fee' | 'skip' | 'cancel'
  amount?: number
  note?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: [result: FeeResult]
}>()

const feeAmount = ref(30) // Default $30 fee
const feeNote = ref(`48-hour ${props.status.toLowerCase()} fee`)

const onAddFee = () => {
  emit('close', {
    action: 'add-fee',
    amount: feeAmount.value,
    note: feeNote.value
  })
}
</script>

<style scoped>
.fee-dialog {
  border-radius: 8px;
  min-width: 400px;
  max-width: 500px;
}

@media (max-width: 480px) {
  .fee-dialog {
    min-width: auto !important;
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    box-sizing: border-box;
  }
}
</style>