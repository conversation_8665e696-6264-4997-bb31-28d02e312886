<template>
  <q-card class="responsive-card">
    <q-form @submit="onSubmit">

      <q-card-section class="q-pt-none form-content">
        <!-- Basic Information -->
        <div class="section-title">
          <q-icon name="person" size="1.2em" class="q-mr-xs" />
          Basic Information
        </div>
        
        <div class="form-grid">
          <div class="form-field">
            <q-input
              outlined
              v-model="firstName"
              label="First Name *"
              :rules="[(val) => (val && val.length > 0) || 'First name is required']"
              :hide-bottom-space="false"
              autofocus
              class="responsive-input"
            />
          </div>
          <div class="form-field">
            <q-input
              outlined
              v-model="lastName"
              label="Last Name *"
              :rules="[(val) => (val && val.length > 0) || 'Last name is required']"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field">
            <q-input
              outlined
              v-model="email"
              label="Email"
              type="email"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field">
            <q-input
              outlined
              v-model="primaryPhone"
              label="Primary Phone *"
              :rules="[(val) => (val && val.length > 0) || 'Primary phone is required']"
              :hide-bottom-space="false"
              placeholder="(*************"
              mask="(###) ###-####"
              class="responsive-input"
            />
          </div>
          <div class="form-field">
            <q-input
              outlined
              v-model="dateOfBirth"
              label="Date of Birth"
              placeholder="DD/MM/YYYY"
              mask="##/##/####"
              :rules="[val => !val || /^\d{2}\/\d{2}\/\d{4}$/.test(val) || 'Please enter a valid date']"
              :hide-bottom-space="false"
              class="responsive-input"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date 
                      v-model="dateOfBirth" 
                      mask="DD/MM/YYYY"
                      :options="date => new Date(date) <= new Date()"
                    >
                      <div class="row items-center justify-end">
                        <q-btn v-close-popup label="Close" color="primary" flat />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          <div class="form-field">
            <q-select
              outlined
              v-model="sex"
              label="Sex"
              :options="['Male', 'Female', 'Other']"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
        </div>

        <!-- Address Information -->
        <q-separator class="section-separator" />
        <div class="section-title">
          <q-icon name="home" size="1.2em" class="q-mr-xs" />
          Address
        </div>
        
        <div class="form-grid">
          <div class="form-field full-width">
            <q-input
              outlined
              v-model="street1"
              label="Street Address"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field full-width">
            <q-input
              outlined
              v-model="street2"
              label="Apartment, Suite, etc."
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field city-field">
            <q-input
              outlined
              v-model="city"
              label="City"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field state-field">
            <q-input
              outlined
              v-model="state"
              label="State"
              :hide-bottom-space="false"
              placeholder="NY"
              mask="AA"
              class="responsive-input"
            />
          </div>
          <div class="form-field zip-field">
            <q-input
              outlined
              v-model="postalCode"
              label="ZIP Code"
              :hide-bottom-space="false"
              placeholder="12345"
              mask="#####"
              class="responsive-input"
            />
          </div>
        </div>

        <!-- Insurance Information -->
        <q-separator class="section-separator" />
        <div class="section-title">
          <q-icon name="medical_services" size="1.2em" class="q-mr-xs" />
          Insurance
        </div>
        
        <div class="insurance-section">
          <!-- Primary Insurance Row -->
          <div class="insurance-row">
            <div class="form-field">
              <q-select
                outlined
                v-model="primaryInsurancePlan"
                label="Primary Insurance Plan *"
                :options="insurancePlanOptions"
                :rules="[(val) => (val && val.length > 0) || 'Primary insurance plan is required']"
                :hide-bottom-space="false"
                use-input
                input-debounce="0"
                @filter="filterInsurancePlans"
                class="responsive-input"
              />
            </div>
            <div class="form-field">
              <q-input
                outlined
                v-model="primaryInsuranceNumber"
                label="Primary Insurance ID"
                :hide-bottom-space="false"
                class="responsive-input"
              />
            </div>
          </div>
          
          <!-- Secondary Insurance Row -->
          <div class="insurance-row">
            <div class="form-field">
              <q-select
                outlined
                v-model="secondaryInsurancePlan"
                label="Secondary Insurance Plan"
                :options="secondaryInsurancePlanOptions"
                :hide-bottom-space="false"
                use-input
                input-debounce="0"
                @filter="filterSecondaryInsurancePlans"
                class="responsive-input"
              />
            </div>
            <div class="form-field">
              <q-input
                outlined
                v-model="secondaryInsuranceNumber"
                label="Secondary Insurance ID"
                :hide-bottom-space="false"
                class="responsive-input"
              />
            </div>
          </div>
        </div>

        <!-- Employment Information -->
        <q-separator class="section-separator" />
        <div class="section-title">
          <q-icon name="work" size="1.2em" class="q-mr-xs" />
          Employment
        </div>
        
        <div class="form-grid">
          <div class="form-field">
            <q-input
              outlined
              v-model="company"
              label="Company"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
          <div class="form-field">
            <q-input
              outlined
              v-model="occupation"
              label="Occupation"
              :hide-bottom-space="false"
              class="responsive-input"
            />
          </div>
        </div>

        <!-- Notes -->
        <q-separator class="section-separator" />
        <div class="section-title">
          <q-icon name="note" size="1.2em" class="q-mr-xs" />
          Additional Notes
        </div>
        
        <div class="form-grid">
          <div class="form-field full-width">
            <q-input
              outlined
              v-model="notes"
              label="Notes"
              type="textarea"
              :rows="$q.screen.xs ? 2 : 3"
              :hide-bottom-space="false"
              autogrow
              class="responsive-input"
            />
          </div>
        </div>

        <div class="required-notice">
          <q-icon name="info" size="16px" />
          <span>Fields marked with * are required</span>
        </div>
      </q-card-section>

      <q-card-actions class="responsive-actions">
        <q-btn
          flat
          label="Cancel"
          @click="onCancel"
          :disable="loading"
          class="action-btn cancel-btn"
        />
        <q-btn
          unelevated
          label="Create Patient"
          type="submit"
          color="primary"
          :loading="loading"
          class="action-btn submit-btn"
          icon-right="add"
        />
      </q-card-actions>
    </q-form>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useClientStore } from 'stores/client'
import { useQuasar } from 'quasar'

const emit = defineEmits<{
  close: [success: boolean, patientName?: string]
}>()

const clientStore = useClientStore()
const $q = useQuasar()

// Basic Information
const firstName = ref('')
const lastName = ref('')
const email = ref('')
const primaryPhone = ref('')
const dateOfBirth = ref('')
const sex = ref('')

// Address Information
const street1 = ref('')
const street2 = ref('')
const city = ref('')
const state = ref('')
const postalCode = ref('')

// Insurance Information
const primaryInsurancePlan = ref('')
const primaryInsuranceNumber = ref('')
const secondaryInsurancePlan = ref('')
const secondaryInsuranceNumber = ref('')

// Employment Information
const company = ref('')
const occupation = ref('')

// Notes
const notes = ref('')

const loading = ref(false)

// Insurance plan options based on your Airtable data
const allInsurancePlans = [
  'Medicare',
  'Aetna',
  'Anthem',
  'Signa',
  'Anthem BCBS',
  'Cigna',
  'GHI/Emblem',
  'NYSHIP/UHC'
]

const insurancePlanOptions = ref([...allInsurancePlans])
const secondaryInsurancePlanOptions = ref([...allInsurancePlans])

// Filter functions for insurance plans
const filterInsurancePlans = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      insurancePlanOptions.value = [...allInsurancePlans]
    } else {
      const needle = val.toLowerCase()
      insurancePlanOptions.value = allInsurancePlans.filter(
        plan => plan.toLowerCase().indexOf(needle) > -1
      )
    }
  })
}

const filterSecondaryInsurancePlans = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      secondaryInsurancePlanOptions.value = [...allInsurancePlans]
    } else {
      const needle = val.toLowerCase()
      secondaryInsurancePlanOptions.value = allInsurancePlans.filter(
        plan => plan.toLowerCase().indexOf(needle) > -1
      )
    }
  })
}

// Computed patient name for display only
const patientName = computed(() => {
  const first = firstName.value.trim()
  const last = lastName.value.trim()
  if (first && last) {
    return `${first} ${last}`
  } else if (first) {
    return first
  } else if (last) {
    return last
  }
  return ''
})

const onSubmit = async () => {
  // Validate required fields
  if (!firstName.value.trim() || !lastName.value.trim() || !primaryPhone.value.trim() || !primaryInsurancePlan.value) {
    return
  }

  loading.value = true
  try {
    const clientData: any = {
      'First Name': firstName.value || undefined,
      'Last Name': lastName.value || undefined,
      'Email': email.value || undefined,
      'Primary Phone': primaryPhone.value || undefined,
      'Date of Birth': dateOfBirth.value || undefined,
      'Sex': sex.value || undefined,
      'Street 1': street1.value || undefined,
      'Street 2': street2.value || undefined,
      'City': city.value || undefined,
      'State': state.value || undefined,
      'Postal Code': postalCode.value || undefined,
      'Company': company.value || undefined,
      'Occupation': occupation.value || undefined,
      'Primary Insurance Plan': primaryInsurancePlan.value || undefined,
      'Primary Insurance #': primaryInsuranceNumber.value || undefined,
      'Secondary Insurance Plan': secondaryInsurancePlan.value || undefined,
      'Secondary Insurance #': secondaryInsuranceNumber.value || undefined,
      'Notes': notes.value || undefined
    }

    // Remove undefined values
    Object.keys(clientData).forEach(key => {
      if (clientData[key] === undefined) {
        delete clientData[key]
      }
    })

    const newClient = await clientStore.createClient(clientData)

    $q.notify({
      type: 'positive',
      message: `Patient "${newClient.fields['Patient Name']}" created successfully`,
      position: 'top'
    })

    emit('close', true, newClient.fields['Patient Name'])
  } catch (error) {
    console.error('Failed to create patient:', error)

    // Show user-friendly error message using Quasar notification
    let errorMessage = 'Failed to create patient. Please try again.'
    if (error.response?.status === 422) {
      errorMessage = 'Invalid patient data. Please check the information and try again.'
    } else if (error.response?.status === 401) {
      errorMessage = 'Authentication error. Please refresh the page and try again.'
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
      timeout: 5000,
    })
  } finally {
    loading.value = false
  }
}

const onCancel = () => {
  emit('close', false)
}
</script>

<style scoped>
/* Base styles */
.responsive-card {
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  min-width: 320px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card-header {
  background: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  flex-shrink: 0;
}

.header-title {
  flex-grow: 1;
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  opacity: 0.6;
  transition: opacity 0.2s;
}

.close-btn:hover {
  opacity: 1;
}

.form-content {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 24px;
  background: #ffffff;
}

/* Custom scrollbar */
.form-content::-webkit-scrollbar {
  width: 8px;
}

.form-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.form-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.form-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Section styles */
.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--q-primary);
  margin-bottom: 16px;
  margin-top: 8px;
}

.section-separator {
  margin: 24px 0 16px 0;
  opacity: 0.15;
}

/* Form grid layout */
.form-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.form-field {
  min-width: 0;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.form-field.city-field {
  grid-column: span 2;
}

.form-field.state-field,
.form-field.zip-field {
  grid-column: span 1;
}

/* Special grid for state and zip to be on same row on mobile */
@media (max-width: 599px) {
  .form-grid:has(.state-field) {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .form-field.city-field {
    grid-column: 1 / -1;
  }
  
  .form-field.state-field,
  .form-field.zip-field {
    grid-column: auto;
    width: 100%;
  }
  
  /* Create a row for state and zip */
  .form-field.state-field {
    display: inline-block;
    width: calc(50% - 6px);
    margin-right: 12px;
  }
  
  .form-field.zip-field {
    display: inline-block;
    width: calc(50% - 6px);
  }
}

/* Input customization */
.responsive-input {
  width: 100%;
}

.responsive-input .q-field__control {
  background: #ffffff;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.responsive-input.q-field--outlined .q-field__control:hover {
  background: #ffffff;
  border-color: var(--q-primary);
}

.responsive-input.q-field--focused .q-field__control {
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(var(--q-primary-rgb), 0.2);
}

/* Insurance section */
.insurance-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insurance-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* Actions */
.responsive-actions {
  padding: 16px 24px;
  background: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  min-width: 120px;
  font-weight: 500;
  text-transform: none;
  border-radius: 8px;
  padding: 10px 24px;
}

.submit-btn {
  box-shadow: 0 2px 8px rgba(var(--q-primary-rgb), 0.25);
}

.required-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f7ff;
  border-radius: 8px;
  font-size: 13px;
  color: #4a5568;
}

/* Tablet styles */
@media (max-width: 1023px) and (min-width: 600px) {
  .responsive-card {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .form-content {
    padding: 20px;
    max-height: calc(100vh - 180px);
  }

  .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .form-field.city-field {
    grid-column: span 1;
  }
}

/* Mobile styles */
@media (max-width: 599px) {
  .responsive-card {
    margin: 0;
    width: 100vw;
    max-width: 100vw;
    min-width: 100vw;
    border-radius: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .card-header {
    padding: 12px 16px;
    position: sticky;
    top: 0;
    z-index: 10;
    background: #ffffff;
  }

  .header-title {
    font-size: 18px;
  }

  .form-content {
    flex: 1;
    padding: 16px;
    max-height: none;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .section-separator {
    margin: 20px 0 12px 0;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .form-field.city-field,
  .form-field.state-field,
  .form-field.zip-field {
    grid-column: span 1;
  }

  /* Insurance rows stack on mobile */
  .insurance-row {
    grid-template-columns: 1fr;
  }

  /* Improved mobile input styling */
  .responsive-input .q-field__control {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .responsive-input .q-field__label {
    font-size: 14px;
  }

  /* Mobile actions */
  .responsive-actions {
    position: sticky;
    bottom: 0;
    z-index: 10;
    padding: 12px 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    background: #ffffff;
    flex-direction: column-reverse;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    min-height: 48px;
  }

  .cancel-btn {
    margin: 0;
  }

  .required-notice {
    font-size: 12px;
    padding: 10px 12px;
  }
}

/* Landscape mobile adjustments */
@media (max-height: 600px) and (max-width: 899px) {
  .form-content {
    max-height: calc(100vh - 140px);
  }

  .responsive-input .q-field__control {
    min-height: 40px;
  }

  .section-separator {
    margin: 16px 0 8px 0;
  }
}

/* Focus and accessibility improvements */
.responsive-input:focus-within {
  outline: 2px solid transparent;
}

.action-btn:focus-visible {
  outline: 2px solid var(--q-primary);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition-property: background-color, border-color, box-shadow;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .responsive-card {
    background: #1a1a1a;
  }

  .card-header {
    background: #fff;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .header-title {
    color: #2a2a2a;
  }

  .form-content {
    background: #fff;
  }

  .responsive-input .q-field__control {
    background: #2a2a2a;
  }

  .responsive-input.q-field--outlined .q-field__control:hover {
    background: #333333;
  }

  .responsive-actions {
    background: #fff;
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .required-notice {
    background: #1e3a5f;
    color: #b0c4de;
  }
}
</style>
