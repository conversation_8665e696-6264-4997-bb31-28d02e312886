<template>
  <q-card v-if="event" style="min-width: 350px; border-radius: 0; box-shadow: none;">
    <q-form @submit="onBeforeSave">
      <q-card-section class="row items-center">
        <div class="text-h6">{{ modalTitle }}</div>
        <q-space />
        <q-checkbox v-if="!isNew && role === 'Admin'" v-model="isCover" label="Cover" />
      </q-card-section>
      <q-linear-progress v-if="_clients.length === 0 && isNew" class="q-mb-md" indeterminate />
      <q-card-section class="q-pt-none">
        <div v-if="isNew" class="row items-center q-pb-lg">
          <q-btn
            class="col-grow q-mr-sm mmm-btn-appointement"
            :class="event.event === 'Appointment' ? 'active' : ''"
            label="Appointment"
            icon="event"
            no-caps
            :disable="readOnly || role !== 'Admin'"
            outline
            @click="event.event = 'Appointment'"
          />
          <q-btn
            btn
            class="col-grow q-ml-sm mmm-btn-appointement"
            :class="event.event === 'Time Off' ? 'active' : ''"
            label="Time Off"
            icon="schedule"
            :disable="readOnly || role !== 'Admin'"
            no-caps
            outline
            @click="event.event = 'Time Off'"
          />
        </div>
        <template v-if="!isTimeoff">
          <!-- Patient Selection -->
          <div class="row q-gutter-sm">
            <div class="col">
              <q-select
                outlined
                dense
                v-model="patient"
                label="Patient"
                use-input
                hide-selected
                fill-input
                input-debounce="0"
                :readonly="role !== 'Admin'"
                :options="options"
                @filter="filterFn"
                :hide-bottom-space="false"
                :rules="[(val) => (val && val.length > 0) || 'Please type something']"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey"> No results </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
            <div class="col-auto">
              <q-btn
                outlined
                dense
                color="primary"
                icon="person_add"
                label="New Patient"
                no-caps
                :disable="role !== 'Admin'"
                @click="newPatientModal = true"
                style="height: 40px"
              />
            </div>
          </div>

          <!-- Patient Info Card -->
          <div
            v-if="patient && !isNew && selectedPatientId"
            class="patient-info-card q-pa-md q-mb-md"
          >
            <div class="row items-center q-gutter-md">
              <div class="col">
                <div v-if="selectedPatientPhone" class="row items-center q-gutter-sm q-mb-xs">
                  <q-icon name="phone" size="sm" color="primary" />
                  <a
                    :href="`tel:${selectedPatientPhone}`"
                    class="phone-link text-primary text-weight-medium"
                    @click.stop
                  >
                    {{ selectedPatientPhone }}
                  </a>
                </div>
                <div v-else class="text-caption text-grey-6 q-mb-xs">
                  <q-icon name="phone_disabled" size="sm" class="q-mr-xs" />
                  No phone number on file
                </div>
                
                <!-- Balance Alert -->
                <div v-if="hasBalanceAlert" class="row items-center q-gutter-sm">
                  <q-icon name="warning" size="sm" color="orange" />
                  <span class="text-orange text-weight-medium">
                    Outstanding Balance: {{ formatCurrency(selectedPatientBalance) }}
                  </span>
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  outline
                  color="primary"
                  icon="open_in_new"
                  label="View Profile"
                  no-caps
                  size="sm"
                  @click="openPatientProfile"
                />
              </div>
            </div>
          </div>

          <q-select
            outlined
            dense
            v-model="event.appointment"
            :options="['Medical', 'Massage', 'Acupuncture']"
            label="Type"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
            :readonly="readOnly"
          />
        </template>
        
        <!-- Full Day Button for Time Off -->
        <div v-if="isTimeoff && isNew" class="q-mb-md">
          <q-btn
            outline
            color="primary"
            icon="schedule"
            label="Full Day"
            no-caps
            class="full-width"
            @click="onFullDayTimeOff"
            :disable="!provider"
          />
          <div class="text-caption text-grey-6 q-mt-xs text-center">
            Automatically creates time-off slots for the entire working day
          </div>
        </div>
        
        <date-input :cover="isCover" :date="date" :current-time="startTime" @change="onChageDate" />
        <q-select
          outlined
          dense
          v-model="provider"
          :options="_providers.map((el) => el.name)"
          label="Provider"
          :rules="[(val) => (val && val.length > 0) || 'Please type something']"
          :hide-bottom-space="false"
          :readonly="role !== 'Admin'"
        />
        <template v-if="!isCover">
          <q-select
            v-if="!isTimeoff"
            outlined
            dense
            v-model="event.status"
            :options="['Pending', 'Confirmed', 'Checked-in', 'Complete', 'Cancelled', 'No Show']"
            label="Status"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
            :readonly="readOnly"
          />
          <div class="gt-md">
            <time-input label="Start Time" :value="startTime" @change="onChangeTime" />
          </div>
          <div class="lt-lg">
            <ios-timer-picker label="Start Time" :value="startTime" @change="onChangeTime" />
          </div>
          <q-select
            outlined
            dense
            v-model="event.duration"
            :readonly="readOnly"
            :options="['15 minutes', '30 minutes', '60 minutes']"
            label="Duration"
            :rules="[(val) => (val && val.length > 0) || 'Please type something']"
            :hide-bottom-space="false"
          />
          <q-input
            v-if="!isTimeoff"
            outlined
            dense
            type="textarea"
            rows="2"
            v-model="event.notes"
            :readonly="readOnly"
          />
        </template>
      </q-card-section>
      <q-card-actions class="text-primary">
        <q-btn
          v-if="!isNew && !readOnly"
          flat
          label="Delete appointment"
          color="red"
          rounded
          unelevated
          no-caps
          @click="onOpenDeleteModal"
        />
        <q-space />
        <q-btn flat label="Cancel" v-close-popup rounded unelevated no-caps />
        <q-btn
          flat
          v-if="!readOnly || isCover"
          type="submit"
          label="Save"
          :disable="disabled"
          :loading="disabled"
          rounded
          unelevated
          no-caps
        />
      </q-card-actions>
    </q-form>
    <q-dialog v-model="alert">
      <q-card style="max-width: 380px">
        <q-card-section>
          <div class="text-h6 text-red">Alert</div>
        </q-card-section>

        <q-card-section class="q-pt-none text-body1">
          Time of the booking is not during the staff member’s working hours.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Agree" color="primary" @click="onSave" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <draggable-modal 
      v-model="newPatientModal" 
      title="Add New Patient"
      :initial-width="600"
      :initial-height="700"
      @close="() => onNewPatientClose(false)"
    >
      <component :is="NewPatientModal" @close="onNewPatientClose" />
    </draggable-modal>

    <!-- 6-Day Warning Dialog -->
    <draggable-modal 
      v-model="sixDayWarningDialog" 
      title="Booking Within 6 Days"
      :initial-width="450"
      :initial-height="300"
      :resizable="false"
      @close="onCancelSixDayWarning"
    >
      <q-card style="box-shadow: none; border-radius: 0;">
        <q-card-section class="q-pt-none">
          <div class="text-orange-8 q-mb-md">
            <q-icon name="warning" class="q-mr-sm" />
            <span class="text-weight-medium">Warning</span>
          </div>
          <p class="text-body1">
            This patient has an appointment within the last 6 days. 
            <span v-if="checkSixDayInterval().lastAppointmentDate">
              Their last appointment was on {{ checkSixDayInterval().lastAppointmentDate }}
              ({{ Math.abs(checkSixDayInterval().daysSince || 0) }} days ago).
            </span>
          </p>
          <p class="text-body2 text-grey-7">
            Are you sure you want to proceed with this booking?
          </p>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn 
            flat 
            label="Cancel" 
            color="grey" 
            @click="onCancelSixDayWarning"
            no-caps
          />
          <q-btn 
            unelevated 
            label="Proceed with Booking" 
            color="orange" 
            @click="onConfirmSixDayWarning"
            no-caps
          />
        </q-card-actions>
      </q-card>
    </draggable-modal>

    <!-- Fee Collection Dialog -->
    <draggable-modal 
      v-model="feeCollectionDialog" 
      title="Cancellation Fee"
      :initial-width="450"
      :initial-height="350"
      :resizable="false"
      @close="() => onFeeCollectionClose({ action: 'cancel' })"
    >
      <component 
        :is="FeeCollectionDialog"
        v-if="feeCollectionData"
        :patient-name="feeCollectionData.patientName"
        :status="feeCollectionData.status"
        @close="onFeeCollectionClose"
      />
    </draggable-modal>
  </q-card>
</template>

<script setup lang="ts">
import moment from 'moment'
import type { ProviderField, StatusType } from '../models'
import { computed, ref, onMounted, watch } from 'vue'
import { useScheduleStore } from 'stores/schedule.js'
import { useClientStore } from 'stores/client.js'
import { useCenterStore } from 'stores/center'
import { useAuthStore } from 'stores/user'
import { storeToRefs } from 'pinia'
import { US_TIME_FORMAT, MAIN_BASE_ID, CLIENT_TABLE_ID } from '../variable'
import TimeInput from 'components/timer/TimeInput.vue'
import DateInput from 'components/timer/DateInput.vue'
import IosTimerPicker from 'components/timer/IosTimerPicker.vue'
import DraggableModal from '../common/DraggableModal.vue'
import { compareDate, generateTimeList, formatCurrency } from 'src/utils'
import { defineAsyncComponent } from 'vue'

const NewPatientModal = defineAsyncComponent(() => import('./NewPatientModal.vue'))
const FeeCollectionDialog = defineAsyncComponent(() => import('./FeeCollectionDialog.vue'))

const props = withDefaults(defineProps<ProviderField>(), {
  name: '',
  event: 'Appointment',
  start: '',
  end: '',
  duration: '',
  notes: '',
  appointment: 'Medical',
  status: '' as StatusType,
  split: 1,
  pid: '',
  group: '',
  cancelled: () => [],
})

const emit = defineEmits(['close'])
const scheduleStore = useScheduleStore()
const clientStore = useClientStore()
const centerStore = useCenterStore()
const authStore = useAuthStore()
const { role } = storeToRefs(authStore)
const { _clients } = storeToRefs(clientStore)
const { providers } = storeToRefs(centerStore)
const event = ref<ProviderField>()
const startTime = ref('')
const endTime = ref('')
const patient = ref('')
const date = ref('')
const provider = ref('')
const disabled = ref(false)
const alert = ref(false)
const isCover = ref(false)
const newPatientModal = ref(false)
const sixDayWarningDialog = ref(false)
const pendingSubmit = ref(false)
const feeCollectionDialog = ref(false)
const feeCollectionData = ref<{
  patientName: string
  patientId: string
  status: 'No Show' | 'Cancelled'
  field: ProviderField
  pid: string
} | null>(null)
const isNew = computed(() => !event.value?.id)
const modalTitle = computed(() => (isNew.value ? 'Create Appointment' : 'Edit Appointment'))
const readOnly = computed(() => isCover.value || role.value !== 'Admin')
const _providers = computed(() => {
  const selectedDate = moment(date.value, US_TIME_FORMAT).toDate()
  return providers.value
    .filter((el) => {
      if (el.startDate && compareDate(el.startDate, selectedDate, 'before')) {
        return false
      }
      if (el.inactiveDate && !compareDate(el.inactiveDate, selectedDate, 'before')) {
        return false
      }
      if (isCover.value) return true
      
      // Check if provider works on this date
      const dayOfWeek = moment(selectedDate).format('dddd')
      const dateSpecificHours = el.getWorkingHoursForDate ? 
        el.getWorkingHoursForDate(selectedDate) : null
      
      if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
        // Provider has date-specific hours for this day
        return true
      }
      
      // Fall back to default working hours
      const flag = el.workingDays.some((el) => el.day === moment(selectedDate).weekday())
      if (!flag) {
        return el.fields.find((field) => compareDate(field.start, selectedDate, 'same'))
      }
      return true
    })
    .filter((provider) => {
      if (event.value?.event === 'Time Off') return true
      const appointment = event.value?.appointment
      if (!appointment) return true
      if (appointment === 'Medical') return provider.role === 'NP' || provider.role == 'PA'
      else if (appointment === 'Massage') return provider.role === 'Massage Therapist'
      else if (appointment === 'Acupuncture') return provider.role === 'Acupuncturist'
      return false
    })
})
const stringOptions = computed(() => _clients.value.map((el) => el.fields['Patient Name']))
const options = ref(stringOptions.value)
const isTimeoff = computed(() => event.value?.event === 'Time Off')
const selectedPatientId = computed(() => {
  if (!patient.value) return null
  const selectedClient = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
  return selectedClient?.id || null
})
const selectedPatientPhone = computed(() => {
  if (!patient.value) return null
  const selectedClient = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
  return selectedClient?.fields['Primary Phone'] || null
})
const selectedPatientBalance = computed(() => {
  if (!patient.value) return 0
  const selectedClient = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
  return selectedClient?.fields['Balance'] || 0
})
const hasBalanceAlert = computed(() => {
  return selectedPatientBalance.value > 0
})
// Watch for appointment type changes and auto-update duration for new appointments
watch(() => event.value?.appointment, (newAppointmentType) => {
  if (!event.value?.id && event.value?.event === 'Appointment' && newAppointmentType) {
    if (newAppointmentType === 'Medical') {
      event.value.duration = '15 minutes'
    } else if (newAppointmentType === 'Massage' || newAppointmentType === 'Acupuncture') {
      event.value.duration = '60 minutes'
    }
  }
})

onMounted(() => {
  event.value = { ...props }
  if (!props.event) event.value.event = 'Appointment'
  
  // Set default prefills for new appointments
  if (!event.value.id && event.value.event === 'Appointment') {
    // Set default status to Pending
    if (!event.value.status) {
      event.value.status = 'Pending'
    }
    
    // Set default duration based on appointment type
    if (!event.value.duration && event.value.appointment) {
      if (event.value.appointment === 'Medical') {
        event.value.duration = '15 minutes'
      } else if (event.value.appointment === 'Massage' || event.value.appointment === 'Acupuncture') {
        event.value.duration = '60 minutes'
      }
    }
  }
  
  const _provider = providers.value.find((el) => el.id === event.value?.pid)
  if (_provider) provider.value = _provider.name
  patient.value = event.value.patientName[0] || ''
  if (!event.value.id) {
    if (props.start) onInitDateTime(props.start)
  } else {
    onInitDateTime(props.start)
  }
})
const onInitDateTime = (start: string | Date) => {
  startTime.value = moment(start, 'YYYY-MM-DD HH:mm').format('HH:mm')
  endTime.value = startTime.value
  date.value = moment(props.start).format(US_TIME_FORMAT)
  if (!startTime.value) {
    const { time } = generateTimeList()
    startTime.value = moment(time, 'hh:mm A').format('HH:mm')
  }
}
const checkWorkingHours = () => {
  const _provider = providers.value.find((el) => el.name === provider.value)
  if (_provider) {
    const mTime = moment.utc(event.value?.start).subtract(4, 'hours').format('H:mm')
    const selectedDate = moment(date.value, US_TIME_FORMAT).toDate()
    const dayOfWeek = moment(selectedDate).format('dddd')
    
    // Get date-specific working hours if available
    const dateSpecificHours = _provider.getWorkingHoursForDate ? 
      _provider.getWorkingHoursForDate(selectedDate) : null
    
    let workingHours: { from: number; to: number } | undefined
    
    if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
      workingHours = {
        from: dateSpecificHours[dayOfWeek].start,
        to: dateSpecificHours[dayOfWeek].end
      }
    } else {
      // Fall back to default working hours
      const day = moment(selectedDate).weekday()
      const workingDay = _provider.workingDays.find((el) => el.day === day)
      if (workingDay) {
        workingHours = {
          from: workingDay.from,
          to: workingDay.to
        }
      }
    }
    
    if (workingHours) {
      const appointmentStartTime = moment(mTime, 'H:mm')
      const workingStartTime = moment(`${Math.floor(workingHours.from)}:${Math.round((workingHours.from % 1) * 60)}`, 'H:mm')
      const workingEndTime = moment(`${Math.floor(workingHours.to)}:${Math.round((workingHours.to % 1) * 60)}`, 'H:mm')
      
      // Get appointment duration
      let durationMinutes = 30; // default
      if (event.value?.duration === '15 minutes') durationMinutes = 15;
      else if (event.value?.duration === '60 minutes') durationMinutes = 60;
      
      const appointmentEndTime = appointmentStartTime.clone().add(durationMinutes, 'minutes')
      
      // Check if both start and end times are within working hours
      const startOk = appointmentStartTime.isSameOrAfter(workingStartTime) && appointmentStartTime.isBefore(workingEndTime)
      const endOk = appointmentEndTime.isAfter(workingStartTime) && appointmentEndTime.isSameOrBefore(workingEndTime)
      
      return startOk && endOk
    }
  }
  return false
}
const checkSixDayInterval = (): { withinSixDays: boolean; lastAppointmentDate?: string; daysSince?: number } => {
  if (!selectedPatientId.value || !event.value?.start) {
    return { withinSixDays: false }
  }
  
  const lastAppointment = scheduleStore.findPatientLastAppointment(selectedPatientId.value, event.value.start)
  
  if (!lastAppointment) {
    return { withinSixDays: false } // No previous appointments, allow booking
  }
  
  const lastAppointmentDate = moment(lastAppointment.fields.Date)
  const newAppointmentDate = moment(event.value.start)
  const daysSince = newAppointmentDate.diff(lastAppointmentDate, 'days')
  
  return {
    withinSixDays: daysSince < 6,
    lastAppointmentDate: lastAppointmentDate.format('MM/DD/YYYY'),
    daysSince
  }
}

const proceedWithSave = async () => {
  if (disabled.value) return
  
  const providerId = providers.value.find((el) => el.name === provider.value)?.id || ''
  if (providerId && event.value) {
    disabled.value = true
    if (isCover.value) {
      await scheduleStore.coverSchedule(providerId, event.value.id)
      emit('close', false)
    } else {
      if (event.value.id) {
        // Handle existing appointment update
        const result = await scheduleStore.updateSchedule(providerId, event.value)
        
        // Check if fee collection is needed
        if (result && typeof result === 'object' && result.needsFeeCollection) {
          feeCollectionData.value = {
            patientName: result.patientName,
            patientId: result.patientId,
            status: result.status as 'No Show' | 'Cancelled',
            field: result.field,
            pid: result.pid
          }
          feeCollectionDialog.value = true
          disabled.value = false
          return
        }
        
        disabled.value = false
        emit('close', false)
      } else {
        // Handle new appointment creation
        const _client = _clients.value.find((el) => el.fields['Patient Name'] === patient.value)
        const patientName = patient.value
        const patientId = _client?.id || ''
        event.value.patientName = [patientName]
        event.value.patientId = [patientId]
        await scheduleStore.createNewAppointement(providerId, event.value)
        emit('close', false)
      }
    }
  }
}

const onFeeCollectionClose = async (result: { action: string; amount?: number; note?: string }) => {
  feeCollectionDialog.value = false
  
  if (feeCollectionData.value) {
    try {
      await scheduleStore.updateScheduleWithFeeCollection(
        feeCollectionData.value.pid,
        feeCollectionData.value.field,
        result
      )
    } catch (error) {
      console.error('Failed to process fee collection:', error)
      // Could show an error toast here
    }
  }
  
  feeCollectionData.value = null
  emit('close', false)
}

const onSave = async () => {
  if (!event.value) return
  
  // Only check 6-day interval for new appointments (not edits)
  if (!event.value.id && event.value.event === 'Appointment') {
    const intervalCheck = checkSixDayInterval()
    
    if (intervalCheck.withinSixDays && !pendingSubmit.value) {
      // Show confirmation dialog
      sixDayWarningDialog.value = true
      disabled.value = false
      return
    }
  }
  
  // Proceed with saving
  await proceedWithSave()
}

const onConfirmSixDayWarning = async () => {
  sixDayWarningDialog.value = false
  pendingSubmit.value = true
  await proceedWithSave()
}

const onCancelSixDayWarning = () => {
  sixDayWarningDialog.value = false
  disabled.value = false
  pendingSubmit.value = false
}
const onBeforeSave = async () => {
  if (event.value) {
    changeTime()
    const checked = checkWorkingHours()
    if (checked) {
      onSave()
    } else {
      alert.value = true
    }
  }
}

const changeTime = () => {
  try {
    const sTime = startTime.value || ''
    const eTime = endTime.value || ''
    const sHour = parseInt(sTime.split(':')[0] || '')
    const sMinute = parseInt(sTime.split(':')[1] || '')
    const eHour = parseInt(eTime.split(':')[0] || '')
    const eMinute = parseInt(eTime.split(':')[1] || '')
    const utcStart = `${moment(date.value, US_TIME_FORMAT).format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    const utcEnd = `${moment(date.value, US_TIME_FORMAT).format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    const utcStartTime = moment.utc(utcStart)
    const utcEndTime = moment.utc(utcEnd)
    utcStartTime.hours(sHour).minutes(sMinute)
    utcEndTime.hours(eHour).minutes(eMinute)
    utcStartTime.add(4, 'hours')
    if (event.value) {
      event.value.start = `${utcStartTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
      event.value.end = `${utcEndTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
    }
  } catch (e) {
    console.error(e)
  }
}
const onChageDate = (val: string, time: string) => {
  date.value = val
  startTime.value = time
  console.log(time)
}
const onChangeTime = (val: string) => {
  if (event.value) startTime.value = val
}
const onOpenDeleteModal = () => {
  emit('close', true)
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const filterFn = (val: string, update: any) => {
  update(() => {
    const needle = val.toLowerCase()
    options.value = stringOptions.value.filter((v) => v.toLowerCase().indexOf(needle) > -1)
  })
}

const openPatientProfile = () => {
  if (selectedPatientId.value) {
    const airtableUrl = `https://airtable.com/${MAIN_BASE_ID}/${CLIENT_TABLE_ID}/${selectedPatientId.value}`
    window.open(airtableUrl, '_blank')
  }
}

const onNewPatientClose = (success: boolean, patientName?: string) => {
  newPatientModal.value = false
  if (success && patientName) {
    // Update the patient selection with the new patient
    patient.value = patientName
    // Refresh the options to include the new patient
    options.value = stringOptions.value
  }
}

const onFullDayTimeOff = async () => {
  if (!provider.value || !date.value) return
  
  const selectedProvider = providers.value.find((p) => p.name === provider.value)
  if (!selectedProvider) return
  
  const selectedDate = moment(date.value, US_TIME_FORMAT).toDate()
  const dayOfWeek = moment(selectedDate).format('dddd')
  
  // Get working hours for the selected date
  let workingHours: { from: number; to: number } | undefined
  
  // Check for date-specific working hours first
  const dateSpecificHours = selectedProvider.getWorkingHoursForDate ? 
    selectedProvider.getWorkingHoursForDate(selectedDate) : null
  
  if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
    workingHours = {
      from: dateSpecificHours[dayOfWeek].start,
      to: dateSpecificHours[dayOfWeek].end
    }
  } else {
    // Fall back to default working hours
    const day = moment(selectedDate).weekday()
    const workingDay = selectedProvider.workingDays.find((el) => el.day === day)
    if (workingDay) {
      workingHours = {
        from: workingDay.from,
        to: workingDay.to
      }
    }
  }
  
  if (!workingHours) {
    // Provider doesn't work on this day
    return
  }
  
  try {
    disabled.value = true
    
    // Calculate time slots (30-minute intervals)
    const startHour = Math.floor(workingHours.from)
    const startMinute = Math.round((workingHours.from % 1) * 60)
    const endHour = Math.floor(workingHours.to)
    const endMinute = Math.round((workingHours.to % 1) * 60)
    
    const startTime = moment().hour(startHour).minute(startMinute).second(0)
    const endTime = moment().hour(endHour).minute(endMinute).second(0)
    
    const timeSlots = []
    // eslint-disable-next-line prefer-const
    let currentTime = startTime.clone()
    
    // Create 30-minute time slots for the entire working day
    while (currentTime.isBefore(endTime)) {
      const slotStart = currentTime.clone()
      const slotEnd = currentTime.clone().add(30, 'minutes')
      
      // Don't create a slot that would go beyond working hours
      if (slotEnd.isAfter(endTime)) {
        break
      }
      
      timeSlots.push({
        start: slotStart,
        end: slotEnd
      })
      
      currentTime.add(30, 'minutes')
    }
    
    // Create time-off appointments for each slot
    const providerId = selectedProvider.id
    
    for (const slot of timeSlots) {
      // Format the datetime exactly like the changeTime function does
      const sHour = slot.start.hour()
      const sMinute = slot.start.minute()
      const utcStart = `${moment(selectedDate).format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`
      const utcStartTime = moment.utc(utcStart)
      utcStartTime.hours(sHour).minutes(sMinute)
      utcStartTime.add(4, 'hours')
      
      const timeOffField: ProviderField = {
        id: '',
        name: 'Time Off',
        event: 'Time Off',
        start: `${utcStartTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`,
        end: `${utcStartTime.format('YYYY-MM-DDTHH:mm:ss.SSS')}Z`, // Same as start for now
        duration: '30 minutes',
        notes: 'Full day time off',
        appointment: 'Medical',
        patientName: [],
        patientId: [],
        status: 'Pending',
        split: 1,
        pid: selectedProvider.pid
      }
      
      await scheduleStore.createNewAppointement(providerId, timeOffField)
    }
    
    disabled.value = false
    emit('close', false)
    
  } catch (error) {
    console.error('Failed to create full day time off:', error)
    disabled.value = false
  }
}
</script>
<style lang="scss">
.mmm-btn-appointement {
  &::before {
    border: 1px solid rgba($color: #000000, $alpha: 0.24);
  }
  .q-icon {
    font-size: 1.3em;
    color: grey;
  }
  &:hover {
    &::before {
      border: 1px solid #6abdff !important;
    }
  }
  &.active {
    &::before {
      border: 1px solid #6abdff !important;
    }
    background-color: #eef7ff !important;
  }
}

.patient-info-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 12px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #6abdff;
    box-shadow: 0 2px 8px rgba(106, 189, 255, 0.15);
  }
}

.phone-link {
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.2s ease;

  &:hover {
    color: #1976d2 !important;
    text-decoration: underline;
  }

  &:active {
    transform: scale(0.98);
  }
}
</style>
