<template>
  <q-card>
    <q-card-section class="row items-center">
      <q-avatar icon="delete_outline" color="red" text-color="white" />
      <span class="q-ml-sm">Are you sure you want to delete this appointment?</span>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat label="Cancel" color="primary" v-close-popup />
      <q-btn
        flat
        label="Confirm"
        color="primary"
        @click="onDelete"
        :disable="disabled"
        :loading="disabled"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import { useStaffStore } from 'stores/staff.js'
import { useScheduleStore } from 'stores/schedule.js'
import type { EventType } from 'components/models.js'
const disabled = ref(false)
const scheduleStore = useScheduleStore()
const staffStore = useStaffStore()
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  type: {
    type: String as PropType<EventType>,
    default: 'Appointment',
  },
})
const emit = defineEmits(['close'])
const onDelete = async () => {
  disabled.value = true
  console.log('Delete clicked for ID:', props.id, 'Type:', props.type)
  
  try {
    if (props.type === 'Appointment') {
      console.log('Deleting as appointment')
      await scheduleStore.deteleAppointment(props.id)
    } else if (props.type === 'Time Off') {
      // Check if this is a full day time off (created as appointment) or regular staff time off
      // Full day time off events are stored in the schedule, not staff table
      const appointment = scheduleStore.schedules.find(schedule => schedule.id === props.id)
      console.log('Searching for time off appointment:', appointment ? 'found' : 'not found')
      
      if (appointment) {
        // This is a full day time off event stored as an appointment
        console.log('Deleting time off as appointment')
        await scheduleStore.deteleAppointment(props.id)
      } else {
        // This is regular staff time off stored in staff table
        console.log('Deleting as staff time off')
        await staffStore.deleteTimeoff(props.id)
      }
    }
    console.log('Delete completed, emitting close')
    emit('close')
  } catch (error) {
    console.error('Delete failed:', error)
    disabled.value = false
  }
}
</script>
