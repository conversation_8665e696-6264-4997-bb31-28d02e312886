<template>
    <div v-if="duration === '15 minutes'" class="col col-shrink row full-height" >
        <div v-if="isNewPatient" class="full-height q-ma-auto q-mr-xs row justify-center">
            <img src="images/new.png" :class="imageClass" />
        </div>
        <div v-if="notes" class="full-height q-ma-auto q-mr-xs row justify-center">
            <img src="images/notes.png" :class="imageClass" />
        </div>
        <div v-if="status === 'Confirmed'" class="full-height q-ma-auto q-mr-xs row justify-center">
            <img src="images/check.png" :class="imageClass" />
        </div>
        <div v-if="isBirthdayWeek" class="full-height q-ma-auto q-mr-xs row justify-center">
            <span :class="emojiClass">🎂</span>
        </div>
        <div v-if="group" class="full-height q-ma-auto row justify-center">
            <div :class="groupClass"></div>
        </div>
    </div>
    <div v-else class="col col-shrink row q-col-gutter-none icon-container" :style="boxStyle" >
        <div v-if="isNewPatient" :class="markClass('isNewPatient')">
            <img src="images/new.png" :class="imageClass" />
        </div>
        <div v-if="notes" :class="markClass('notes')">
            <img src="images/notes.png" :class="imageClass" />
        </div>
        <div v-if="status === 'Confirmed'" :class="markClass('status')">
            <img src="images/check.png" :class="imageClass" />
        </div>
        <div v-if="isBirthdayWeek" :class="markClass('isBirthdayWeek')">
            <span :class="emojiClass">🎂</span>
        </div>
        <div v-if="group" :class="markClass('group')">
            <div :class="groupClass"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from 'vue'
import type { StatusType } from '../models'
const props = defineProps({
    isNewPatient: {
        type: Boolean,
        default: false
    },
    notes: {
        type: String,
    },
    status: {
        type: String as PropType<StatusType>
    },
    group: {
        type: String,
    },
    duration: {
        type: String,
    },
    isBirthdayWeek: {
        type: Boolean,
        default: false
    }
})
const imageClass = computed(() => {
    if (props.duration === '15 minutes') return 'sm-image'
    else if (props.duration === '30 minutes') return 'sm-image image-30-margin'
    return 'md-image'
})
const emojiClass = computed(() => {
    if (props.duration === '15 minutes') return 'sm-emoji'
    else if (props.duration === '30 minutes') return 'sm-emoji emoji-30-margin'
    return 'md-emoji'
})
const groupClass = computed(() => {
    if (props.duration === '15 minutes' || props.duration === '30 minutes') return 'glossy-blue group-margin'
    return 'glossy-blue'
})
const boxStyle = computed(() => {
    if (props.duration === '30 minutes') return 'height: 26px; min-width: 24px; width: auto;'
    return 'height: 34px; min-width: 24px; width: auto;'
})
const markClass = computed(() => {
    return (param: string) => {
        const iconCount = [props.isNewPatient, props.notes, props.status === 'Confirmed', props.group, props.isBirthdayWeek].filter(Boolean).length
        
        if(iconCount === 1) {
            if(param === 'isNewPatient' && props.isNewPatient) return 'col-12 row justify-end q-px-xs'
            if(param === 'notes' && props.notes) return 'col-12 row justify-end q-px-xs'
            if(param === 'status' && props.status === 'Confirmed') return 'col-12 row justify-end q-px-xs'
            if(param === 'group' && props.group) return 'col-12 row justify-end q-px-xs'
            if(param === 'isBirthdayWeek' && props.isBirthdayWeek) return 'col-12 row justify-end q-px-xs'
        } else if(iconCount === 2) {
            return 'col-auto row justify-end q-px-xs'
        } else if(iconCount === 3) {
            return 'col-4 row justify-end q-px-xs'
        } else if(iconCount <= 5) {
            return 'col-auto row justify-end q-px-xs'
        }
        return 'col-auto row justify-end q-px-xs'
    }
})
</script>
<style lang="scss">
.md-image {
    height: 16px;
    object-fit: contain;
}
.sm-image {
    height: 13px;
    object-fit: contain;
}
.md-emoji {
    font-size: 14px;
    line-height: 16px;
}
.sm-emoji {
    font-size: 11px;
    line-height: 13px;
}
.emoji-30-margin {
    margin-top: -3px;
}
.group-margin {
    margin-top: 1px !important;
}
.image-30-margin {
    margin-top: -3px;
}
.glossy-blue {
  width: 12px;
  height: 12px;
  margin-top: 2px;
  position: relative;
  background-image: url('/images/dot.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 50%;
}

.glossy-blue::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: rgba(255, 255, 255, 0.3); /* Light gloss reflection */
  border-radius: 10px;
  box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.3); /* Inner white glow */
  pointer-events: none; /* Avoid interaction with the overlay */
}

.icon-container {
  flex-wrap: wrap;
  gap: 2px;
  align-content: flex-start;
  align-items: flex-start;
  max-width: none !important;
  width: auto !important;
}
</style>
