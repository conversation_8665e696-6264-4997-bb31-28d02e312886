import { ref, getCurrentInstance } from 'vue'
import moment from 'moment'

// Global state to track which component owns each time slot's + button
const activeAddButtons = ref(new Map<string, string>())

export const useAddButtonCoordination = () => {
  const instance = getCurrentInstance()
  const componentId = instance?.uid?.toString() || Math.random().toString()

  // Generate unique time slot identifier
  const getTimeSlotId = (start: string | Date, date?: string) => {
    const startMoment = moment(start)
    const dateKey = date || startMoment.format('YYYY-MM-DD')
    const timeKey = startMoment.format('HH:mm')
    return `${dateKey}-${timeKey}`
  }

  // Check if another component has registered for this time slot
  const hasOtherButtonInTimeSlot = (timeSlotId: string) => {
    const owner = activeAddButtons.value.get(timeSlotId)
    return owner !== undefined && owner !== componentId
  }

  // Check if this component owns the button for this time slot
  const ownsButtonForTimeSlot = (timeSlotId: string) => {
    return activeAddButtons.value.get(timeSlotId) === componentId
  }

  // Register this component as having a + button for the time slot
  const registerAddButton = (timeSlotId: string) => {
    if (!activeAddButtons.value.has(timeSlotId)) {
      activeAddButtons.value.set(timeSlotId, componentId)
      return true
    }
    return false
  }

  // Unregister this component's + button
  const unregisterAddButton = (timeSlotId: string) => {
    if (activeAddButtons.value.get(timeSlotId) === componentId) {
      activeAddButtons.value.delete(timeSlotId)
    }
  }

  // Clear all registered buttons (useful for date changes)
  const clearAllAddButtons = () => {
    activeAddButtons.value.clear()
  }

  return {
    getTimeSlotId,
    hasOtherButtonInTimeSlot,
    ownsButtonForTimeSlot,
    registerAddButton,
    unregisterAddButton,
    clearAllAddButtons
  }
}