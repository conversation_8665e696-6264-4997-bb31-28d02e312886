import moment from 'moment'
import { computed } from 'vue'
import { Platform } from 'quasar'
import { storeToRefs } from 'pinia'
import type { AppointmentType, EventType, ProviderField } from 'src/components/models'
import { GROUP_COLORS, US_TIME_FORMAT } from 'src/components/variable'
import { useFilterStore } from 'src/stores/filter'
import { useCenterStore } from 'stores/center'
import { useClientStore } from 'stores/client'
import { useAuthStore } from 'stores/user'
import { compareDate } from 'src/utils'

export const useEvent = () => {
  const centerStore = useCenterStore()
  const filterStore = useFilterStore()
  const clientStore = useClientStore()
  const authStore = useAuthStore()
  const { role, name } = storeToRefs(authStore)
  const { providers } = storeToRefs(centerStore)
  const { _date, providerId, eventType, appointmentType, patientName } = storeToRefs(filterStore)
  const { _clients } = storeToRefs(clientStore)

  const selectedDate = computed(() => {
    return moment(_date.value, US_TIME_FORMAT).toDate()
  })
  const _providers = computed(() => {
    let sortProviders = [...providers.value]
    sortProviders = sortProviders.sort((a, b) => {
      if (role.value === 'Staff') {
        if (name.value.includes('ACU ')) {
          if (a.name.includes('ACU')) return -1
          if (b.name.includes('ACU')) return 1
          return a.name.localeCompare(b.name)
        } else {
          if (a.name === name.value) return -1
          if (b.name === name.value) return 1
          return -1
        }
      }

      if (!a.role || !b.role) return 1
      const roleValue = b.role.localeCompare(a.role)
      if (roleValue !== 0) return roleValue
      return a.name.localeCompare(b.name)
    })
    return sortProviders
      .filter((el) => {
        if (providerId.value && providerId.value !== el.id) return false
        if (el.startDate && compareDate(el.startDate, selectedDate.value, 'before')) {
          return false
        }
        if (el.inactiveDate && !compareDate(el.inactiveDate, selectedDate.value, 'before')) {
          return false
        }
        // Check if provider works on this date
        const dayOfWeek = moment(selectedDate.value).format('dddd')
        const dateSpecificHours = el.getWorkingHoursForDate ? 
          el.getWorkingHoursForDate(selectedDate.value) : null
        
        if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
          // Provider has date-specific hours for this day
          return true
        }
        
        // Fall back to default working hours
        const flag = el.workingDays.some((el) => el.day === moment(selectedDate.value).weekday())
        if (!flag) {
          return el.fields.find((field) => compareDate(field.start, selectedDate.value, 'same'))
        }
        return true
      })
      .map((el, index) => ({
        ...el,
        id: index + 1,
        label: el.name,
        class:
          el.role === 'PA' || el.role === 'NP'
            ? `split${index + 1} np-pa-flex-width`
            : `split${index + 1}`,
        pid: el.id,
      }))
  })
  const allEvents = computed(() => {
    let events: ProviderField[] = []
    const cancelledEvents: ProviderField[] = []
    _providers.value.forEach((provider) => {
      provider.fields.forEach((field) => {
        const flag = filter(
          field,
          selectedDate.value,
          eventType.value,
          appointmentType.value,
          patientName.value,
        )
        if (flag) {
          if (field.event === 'Appointment') {
            const client = _clients.value.find((client) => field.patientId.includes(client.id))
            if (client) {
              // Check if birthday is in the same week as appointment
              let isBirthdayWeek = false
              if (client.fields['Date of Birth']) {
                // Parse birthday - handle both DD.MM.YYYY and M.D.YYYY formats
                let birthDate
                const dateStr = client.fields['Date of Birth']
                if (dateStr.includes('.')) {
                  const parts = dateStr.split('.')
                  if (parts.length >= 3 && parts[0] && parts[1]) {
                    if (parts[0].length <= 2 && parts[1].length <= 2) {
                      // Assume DD.MM.YYYY format
                      birthDate = moment(dateStr, 'DD.MM.YYYY')
                    } else {
                      // Assume M.D.YYYY format
                      birthDate = moment(dateStr, 'M.D.YYYY')
                    }
                  }
                } else {
                  birthDate = moment(dateStr)
                }
                
                if (birthDate && birthDate.isValid()) {
                  const appointmentDate = moment(field.start)
                  // Set the birth date to the current year for comparison
                  const birthDateThisYear = birthDate.clone().year(appointmentDate.year())
                  // Check if birthday falls within the same week as the appointment
                  const appointmentWeekStart = appointmentDate.clone().startOf('week')
                  const appointmentWeekEnd = appointmentDate.clone().endOf('week')
                  isBirthdayWeek = birthDateThisYear.isBetween(appointmentWeekStart, appointmentWeekEnd, 'day', '[]')
                }
              }
              
              const event = {
                ...field,
                split: provider.id,
                pid: provider.pid,
                isNewPatient: !client.fields['Last Visit Date'] || false,
                isBirthdayWeek,
              }
              if (field.status !== 'Cancelled') events.push(event)
              else cancelledEvents.push(event)
            }
          } else if (field.event === 'Time Off') {
            events.push({
              ...field,
              split: provider.id,
              pid: provider.pid,
              isNewPatient: false,
            })
          }
        }
      })
    })
    const clientIds = events.map((el) => el.patientId).flat()
    const counts = clientIds.reduce(
      (acc, element) => {
        if (acc[element]) {
          acc[element]++
        } else {
          acc[element] = 1
        }
        return acc
      },
      {} as Record<string, number>,
    )
    const groups = Object.entries(counts)
      .map(([id, count]) => ({
        id,
        count,
      }))
      .filter((el) => el.count > 1)
    events = events.map((el, eventIndex) => {
      const patiendId = el.patientId[0]
      const index = groups.findIndex((el) => el.id === patiendId)
      
      // No cancelled events attached to individual appointments anymore
      const event = { ...el, cancelled: [] }
      if (index < 0) return event
      return { ...event, group: GROUP_COLORS[index % GROUP_COLORS.length] } as ProviderField
    })
    
    // Group ALL cancelled events by time slot only (not by provider)
    const cancelledGroups = cancelledEvents.reduce((groups, cancelledEvent) => {
      const key = `${cancelledEvent.start}-${cancelledEvent.duration}`
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(cancelledEvent)
      return groups
    }, {} as Record<string, typeof cancelledEvents>)
    
    // Create container events for cancelled appointment groups
    Object.values(cancelledGroups).forEach((group) => {
      if (group.length > 0) {
        // Use the leftmost provider (first in the provider list) for consistent positioning
        const leftmostProvider = _providers.value[0]
        const containerEvent = {
          ...group[0], // Use first cancelled appointment as base
          pid: leftmostProvider.id, // Always use leftmost provider for positioning
          id: `cancelled-group-${group[0].start}-${group[0].duration}`,
          name: `${group.length} Cancelled Appointment${group.length > 1 ? 's' : ''}`,
          status: 'CancelledGroup' as const,
          cancelledAppointments: group,
          totalCancelledInSlot: group.length
        }
        events.push(containerEvent)
      }
    })
    return events
  })
  const timeRange = computed(() => {
    const dayOfWeek = moment(selectedDate.value).format('dddd')
    const workingHours: { from: number; to: number }[] = []
    
    _providers.value.forEach((provider) => {
      // Get date-specific working hours if available
      const dateSpecificHours = provider.getWorkingHoursForDate ? 
        provider.getWorkingHoursForDate(selectedDate.value) : null
      
      if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
        workingHours.push({
          from: dateSpecificHours[dayOfWeek].start,
          to: dateSpecificHours[dayOfWeek].end
        })
      } else {
        // Fall back to default working hours
        const defaultHours = provider.workingDays.find(
          (el) => el.day === moment(selectedDate.value).weekday()
        )
        if (defaultHours) {
          workingHours.push({
            from: defaultHours.from,
            to: defaultHours.to
          })
        }
      }
    })
    
    if (workingHours.length === 0) return { min: 12 * 60, max: 14 * 60, rMin: 12, rMax: 14 }
    const minH = Math.min(...workingHours.map((el) => el.from)) - 1
    const maxH = Math.max(...workingHours.map((el) => el.to)) + 1
    return {
      min: minH * 60,
      max: maxH * 60,
      rMin: minH,
      rMax: maxH,
      height: !Platform.is.mobile
        ? Math.max(40, Math.floor((window.screen.availHeight - 300) / ((maxH - minH) * 2)))
        : 50,
    }
  })
  const selectedDateEvents = computed(() => {
    const _events: ProviderField[] = [...allEvents.value]
    const dayOfWeek = moment(selectedDate.value).format('dddd')
    
    _providers.value.forEach((provider) => {
      // Get date-specific working hours if available
      const dateSpecificHours = provider.getWorkingHoursForDate ? 
        provider.getWorkingHoursForDate(selectedDate.value) : null
      
      let workingDay: { from: number; to: number } | undefined
      
      if (dateSpecificHours && dateSpecificHours[dayOfWeek]) {
        workingDay = {
          from: dateSpecificHours[dayOfWeek].start,
          to: dateSpecificHours[dayOfWeek].end
        }
      } else {
        // Fall back to default working hours
        const defaultDay = provider.workingDays.find(
          (el) => el.day === moment(selectedDate.value).weekday()
        )
        if (defaultDay) {
          workingDay = {
            from: defaultDay.from,
            to: defaultDay.to
          }
        }
      }
      const field = {
        name: '',
        status: 'No Show',
        duration: '',
        notes: 'event',
        event: 'Appointment',
        appointment: 'Acupuncture',
        id: '',
        patientId: [] as string[],
        patientName: [] as string[],
        split: provider.id,
        non_event: true,
      } as ProviderField
      if (workingDay) {
        const { rMin, rMax } = timeRange.value
        if (rMin < workingDay.from) {
          _events.push({
            ...field,
            start: moment(selectedDate.value).hours(rMin).minutes(0).format('YYYY-MM-DD HH:mm'),
            end: moment(selectedDate.value)
              .hours(Math.floor(workingDay.from))
              .minutes(Math.round((workingDay.from % 1) * 60))
              .format('YYYY-MM-DD HH:mm'),
            non_event: true,
            background: true,
          })
        }
        if (rMax > workingDay.to) {
          _events.push({
            ...field,
            start: moment(selectedDate.value)
              .hours(Math.floor(workingDay.to))
              .minutes(Math.round((workingDay.to % 1) * 60))
              .format('YYYY-MM-DD HH:mm'),
            end: moment(selectedDate.value).hours(rMax).minutes(0).format('YYYY-MM-DD HH:mm'),
            non_event: true,
            background: true,
          })
        }
      }
    })
    return _events
  })
  const filter = (
    field: ProviderField,
    selectedDate: Date,
    eventType: EventType,
    appointmentType: AppointmentType,
    patientName: string,
  ) => {
    const momentA = moment(selectedDate)
    const momentB = moment(field.start, 'YYYY-MM-DD')
    if (!momentA.isSame(momentB)) return false
    if (eventType) return field.event === eventType
    if (appointmentType) return field.appointment === appointmentType
    if (patientName) {
      const name = field.patientName[0]
      if (!name) return false
      return name.toLowerCase().includes(patientName.toLowerCase())
    }
    return true
  }
  return {
    providers: _providers,
    timeRange,
    events: selectedDateEvents,
    selectedDate,
  }
}
