<template>
  <q-layout style="height: 100vh" view="lHh Lpr lff" container>
    <q-header class="bg-white simple-header" elevated>
      <div class="simple-header-content">
        <q-btn class="lt-md" size="sm" color="grey" icon="menu" round @click="onOpen" />
        <q-btn color="grey" size="sm" round label="A" class="q-ml-auto">
          <q-menu>
            <div style="width: 150px">
              <q-item clickable v-close-popup @click="onExit">
                <q-item-section avatar>
                  <q-icon color="primary" name="logout" />
                </q-item-section>
                <q-item-section>Log out</q-item-section>
              </q-item>
            </div>
          </q-menu>
        </q-btn>
      </div>
    </q-header>
    <left-drawer />
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import LeftDrawer from 'components/LeftDrawer.vue'
import { useFilterStore } from 'stores/filter'
import { useScheduleStore } from 'stores/schedule'
import { useTestStore } from 'stores/test'
import { useAuth } from 'src/composables/useAuth'
import { MMM_CRED, US_TIME_FORMAT } from 'src/components/variable'
import moment from 'moment'
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
const router = useRouter()
const scheduleStore = useScheduleStore()
const filterStore = useFilterStore()
const testStore = useTestStore()
const auth = useAuth()
const { _date, _patientName } = storeToRefs(filterStore)
const selectedDate = computed(() =>
  moment(_date.value, US_TIME_FORMAT).format('dddd, MMMM D, YYYY'),
)
const patientName = computed({
  get: () => _patientName.value,
  set: (val) => filterStore.changePatient(val),
})
const onExit = async () => {
  localStorage.removeItem(MMM_CRED)
  await auth.logout()
  router.push('/login')
}
const onToday = () => {
  const date = moment().format(US_TIME_FORMAT)
  filterStore.changeDate(date)
}
const onPrev = async () => {
  const prevMoment = moment(_date.value, US_TIME_FORMAT).subtract(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(prevMoment)
  filterStore.changeDate(prevMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
const onNext = async () => {
  const nextMoment = moment(_date.value, US_TIME_FORMAT).add(1, 'day')
  const hasSchedule = scheduleStore.hasSchedule(nextMoment)
  filterStore.changeDate(nextMoment.format(US_TIME_FORMAT))
  if (!hasSchedule) {
    filterStore.loading = true
    await scheduleStore.getSchedules()
  }
  filterStore.loading = false
}
const onOpen = () => {
  filterStore.setOpen()
}
const onUpload = () => {
  testStore.uploadBulkAppointment()
}
</script>
<style lang="scss">
// Simple header styles
.simple-header {
  height: 60px;
  border: none;
  position: relative !important;
}

.simple-header-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

// Adjust page container to not have top padding
.q-page-container {
  padding-top: 0 !important;
}
</style>
