{"permissions": {"allow": ["mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_click", "mcp__playwright__browser_press_key", "Bash(firebase deploy:*)", "<PERSON><PERSON>(firebase login:*)", "Bash(npm run build:*)", "Bash(npm run deploy:*)", "Bash(npm run dev:*)", "Bash(git commit:*)", "mcp__playwright__browser_snapshot", "<PERSON><PERSON>(cat:*)", "Bash(node:*)", "mcp__airtable__list_records", "mcp__airtable__search_records", "<PERSON><PERSON>(pkill:*)", "Bash(rg:*)", "Bash(git pull:*)", "Bash(git reset:*)", "<PERSON><PERSON>(git clean:*)", "mcp__airtable__describe_table", "Bash(open /Users/<USER>/Documents/GitHub/Custom-Scheduling-Web-App/debug-schedule.html)", "Bash(firebase functions:delete:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(firebase functions:list:*)", "Bash(firebase functions:log:*)", "Bash(firebase firestore:get:*)", "Bash(ls:*)", "Bash(npm install)", "Bash(rm:*)", "Bash(firebase functions:config:get:*)", "Bash(npm run serve:*)", "Bash(grep:*)", "Bash(npm run lint)", "Bash(npx update-browserslist-db:*)", "Bash(firebase firestore:export:*)", "Bash(find:*)", "Bash(npx tsc:*)", "mcp__firebase__firebase_get_environment", "Bash(firebase functions:shell:*)", "<PERSON><PERSON>(mv:*)", "Bash(firebase functions:call:*)", "mcp__firebase__firestore_query_collection", "mcp__airtable__list_bases"], "deny": [], "defaultMode": "acceptEdits"}}